const { defineConfig } = require('@vue/cli-service');

module.exports = defineConfig({
  // 开发服务器配置
  devServer: {
    port: 8080,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        ws: true,
      },
    },
  },

  // 生产构建配置
  productionSourceMap: false,

  // 禁用ESLint
  lintOnSave: false,

  // Webpack 配置
  configureWebpack: {
    resolve: {
      extensions: ['.ts', '.js', '.vue', '.json'],
    },
  },

  // CSS 配置
  css: {
    loaderOptions: {
      less: {
        lessOptions: {
          javascriptEnabled: true,
        },
      },
    },
  },
});
