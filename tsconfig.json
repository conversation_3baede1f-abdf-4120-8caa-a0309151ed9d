{"compilerOptions": {"target": "esnext", "module": "esnext", "lib": ["esnext", "dom", "dom.iterable"], "moduleResolution": "node", "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "strict": true, "noImplicitAny": false, "skipLibCheck": true, "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "jsx": "preserve", "useDefineForClassFields": true, "sourceMap": true, "noEmit": true, "types": ["webpack-env"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["node_modules", "dist"]}