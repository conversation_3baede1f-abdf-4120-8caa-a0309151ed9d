<template>
  <teleport to="body">
    <transition-group name="toast" tag="div" class="toast-container">
      <div
        v-for="toast in toasts"
        :key="toast.id"
        :class="[
          'toast-item',
          `toast-${toast.type}`,
          { 'toast-entering': toast.entering }
        ]"
      >
        <div class="toast-content">
          <!-- 图标 -->
          <div class="toast-icon">
            <svg v-if="toast.type === 'success'" viewBox="0 0 24 24" width="24" height="24">
              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill="currentColor"/>
            </svg>
            <svg v-else-if="toast.type === 'error'" viewBox="0 0 24 24" width="24" height="24">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="currentColor"/>
            </svg>
            <svg v-else-if="toast.type === 'warning'" viewBox="0 0 24 24" width="24" height="24">
              <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z" fill="currentColor"/>
            </svg>
            <svg v-else-if="toast.type === 'info'" viewBox="0 0 24 24" width="24" height="24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z" fill="currentColor"/>
            </svg>
          </div>
          
          <!-- 消息内容 -->
          <div class="toast-message">
            <div class="toast-title">{{ toast.title }}</div>
            <div v-if="toast.description" class="toast-description">{{ toast.description }}</div>
          </div>
          
          <!-- 关闭按钮 -->
          <button class="toast-close" @click="removeToast(toast.id)">
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="currentColor"/>
            </svg>
          </button>
        </div>
        
        <!-- 进度条 -->
        <div v-if="toast.showProgress" class="toast-progress">
          <div 
            class="toast-progress-bar" 
            :style="{ animationDuration: `${toast.duration}ms` }"
          ></div>
        </div>
      </div>
    </transition-group>
  </teleport>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const toasts = ref([]);
let toastId = 0;

const addToast = (options) => {
  const id = ++toastId;
  const toast = {
    id,
    type: options.type || 'info',
    title: options.title || options.message,
    description: options.description,
    duration: options.duration || 4000,
    showProgress: options.showProgress !== false,
    entering: true,
  };
  
  toasts.value.push(toast);
  
  // 移除entering状态
  setTimeout(() => {
    const toastItem = toasts.value.find(t => t.id === id);
    if (toastItem) {
      toastItem.entering = false;
    }
  }, 100);
  
  // 自动移除
  if (toast.duration > 0) {
    setTimeout(() => {
      removeToast(id);
    }, toast.duration);
  }
  
  return id;
};

const removeToast = (id) => {
  const index = toasts.value.findIndex(t => t.id === id);
  if (index > -1) {
    toasts.value.splice(index, 1);
  }
};

const clearAll = () => {
  toasts.value = [];
};

// 暴露方法给外部使用
defineExpose({
  addToast,
  removeToast,
  clearAll,
});

// 全局注册
onMounted(() => {
  window.$customToast = {
    success: (title, description, options = {}) => addToast({ 
      type: 'success', 
      title, 
      description, 
      ...options 
    }),
    error: (title, description, options = {}) => addToast({ 
      type: 'error', 
      title, 
      description, 
      ...options 
    }),
    warning: (title, description, options = {}) => addToast({ 
      type: 'warning', 
      title, 
      description, 
      ...options 
    }),
    info: (title, description, options = {}) => addToast({ 
      type: 'info', 
      title, 
      description, 
      ...options 
    }),
    custom: addToast,
    clear: clearAll,
  };
});
</script>

<style scoped>
.toast-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.toast-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  border-radius: 12px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 12px;
  min-width: 320px;
  max-width: 420px;
  overflow: hidden;
  pointer-events: auto;
  position: relative;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.toast-content {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  gap: 12px;
}

.toast-icon {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2px;
}

.toast-success {
  border-left: 4px solid #10b981;
}

.toast-success .toast-icon {
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.toast-error {
  border-left: 4px solid #ef4444;
}

.toast-error .toast-icon {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.toast-warning {
  border-left: 4px solid #f59e0b;
}

.toast-warning .toast-icon {
  color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
}

.toast-info {
  border-left: 4px solid #3b82f6;
}

.toast-info .toast-icon {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}

.toast-message {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
  line-height: 1.4;
  margin-bottom: 2px;
}

.toast-description {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
}

.toast-close {
  flex-shrink: 0;
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  margin-top: -2px;
}

.toast-close:hover {
  color: #6b7280;
  background: rgba(0, 0, 0, 0.05);
}

.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.toast-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  width: 100%;
  animation: toast-progress linear forwards;
  transform-origin: left;
}

.toast-success .toast-progress-bar {
  background: linear-gradient(90deg, #10b981, #059669);
}

.toast-error .toast-progress-bar {
  background: linear-gradient(90deg, #ef4444, #dc2626);
}

.toast-warning .toast-progress-bar {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.toast-info .toast-progress-bar {
  background: linear-gradient(90deg, #3b82f6, #2563eb);
}

@keyframes toast-progress {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(0);
  }
}

/* 过渡动画 */
.toast-enter-active {
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.toast-leave-active {
  transition: all 0.3s ease-in;
}

.toast-enter-from {
  opacity: 0;
  transform: translateY(-20px) scale(0.9);
}

.toast-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.toast-move {
  transition: transform 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .toast-container {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: calc(100% - 20px);
    max-width: 400px;
  }

  .toast-item {
    min-width: auto;
    max-width: none;
    width: 100%;
  }
}
</style>
