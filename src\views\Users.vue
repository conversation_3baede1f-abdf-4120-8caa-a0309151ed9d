<template>
  <div class="personnel">
    <div class="page-header">
      <h2>人员管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新增人员
        </el-button>
        <el-button @click="refreshUsers">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchQuery"
            placeholder="搜索姓名或邮箱"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="roleFilter" placeholder="角色" @change="handleFilter">
            <el-option label="全部角色" value="" />
            <el-option label="管理员" value="admin" />
            <el-option label="项目经理" value="manager" />
            <el-option label="成员" value="member" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="状态" @change="handleFilter">
            <el-option label="全部状态" value="" />
            <el-option label="活跃" value="active" />
            <el-option label="非活跃" value="inactive" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="departmentFilter" placeholder="部门" @change="handleFilter">
            <el-option label="全部部门" value="" />
            <el-option label="技术部" value="技术部" />
            <el-option label="产品部" value="产品部" />
            <el-option label="设计部" value="设计部" />
            <el-option label="运营部" value="运营部" />
          </el-select>
        </el-col>
      </el-row>
    </div>

    <!-- 人员表格 -->
    <el-table
      :data="filteredUsers"
      style="width: 100%"
      @row-click="openUserDetail"
    >
      <el-table-column label="头像" width="80">
        <template #default="{ row }">
          <el-avatar :size="40" :src="row.avatar">
            {{ row.name?.charAt(0) }}
          </el-avatar>
        </template>
      </el-table-column>

      <el-table-column prop="name" label="姓名" width="120" />

      <el-table-column prop="username" label="用户名" width="120" />

      <el-table-column prop="email" label="邮箱" min-width="180" />

      <el-table-column prop="role" label="角色" width="100">
        <template #default="{ row }">
          <el-tag :type="getRoleType(row.role)" size="small">
            {{ getRoleText(row.role) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="department" label="部门" width="120" />

      <el-table-column prop="phone" label="电话" width="140" />

      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)" size="small">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="createdAt" label="创建时间" width="120">
        <template #default="{ row }">
          {{ formatDate(row.createdAt) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="180" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click.stop="editUser(row)">
            编辑
          </el-button>
          <el-button
            size="small"
            :type="row.status === 'active' ? 'warning' : 'success'"
            @click.stop="toggleUserStatus(row)"
          >
            {{ row.status === 'active' ? '禁用' : '启用' }}
          </el-button>
          <el-button size="small" type="danger" @click.stop="deleteUser(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建用户对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="新增人员"
      width="600px"
    >
      <el-form
        ref="userFormRef"
        :model="userForm"
        :rules="userFormRules"
        label-width="100px"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="userForm.name" placeholder="请输入姓名" />
        </el-form-item>

        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>

        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色">
            <el-option label="管理员" value="admin" />
            <el-option label="项目经理" value="manager" />
            <el-option label="成员" value="member" />
          </el-select>
        </el-form-item>

        <el-form-item label="部门" prop="department">
          <el-select v-model="userForm.department" placeholder="请选择部门">
            <el-option label="技术部" value="技术部" />
            <el-option label="产品部" value="产品部" />
            <el-option label="设计部" value="设计部" />
            <el-option label="运营部" value="运营部" />
          </el-select>
        </el-form-item>

        <el-form-item label="电话" prop="phone">
          <el-input v-model="userForm.phone" placeholder="请输入电话号码" />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            v-model="userForm.password"
            type="password"
            placeholder="请输入初始密码"
            show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createUser">确定</el-button>
      </template>
    </el-dialog>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="人员详情"
      width="600px"
    >
      <div v-if="selectedUser" class="user-detail">
        <div class="user-info">
          <el-avatar :size="80" :src="selectedUser.avatar">
            {{ selectedUser.name?.charAt(0) }}
          </el-avatar>
          <div class="info-content">
            <h3>{{ selectedUser.name }}</h3>
            <p>{{ selectedUser.email }}</p>
            <div class="tags">
              <el-tag :type="getRoleType(selectedUser.role)">
                {{ getRoleText(selectedUser.role) }}
              </el-tag>
              <el-tag :type="getStatusType(selectedUser.status)">
                {{ getStatusText(selectedUser.status) }}
              </el-tag>
            </div>
          </div>
        </div>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户名">
            {{ selectedUser.username }}
          </el-descriptions-item>
          <el-descriptions-item label="部门">
            {{ selectedUser.department || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="电话">
            {{ selectedUser.phone || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDate(selectedUser.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后更新">
            {{ formatDate(selectedUser.updatedAt) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Plus,
  Refresh,
  Search,
} from '@element-plus/icons-vue';
import type { User, UserForm } from '@/types';

const showCreateDialog = ref(false);
const showDetailDialog = ref(false);
const selectedUser = ref<User | null>(null);
const searchQuery = ref('');
const roleFilter = ref('');
const statusFilter = ref('');
const departmentFilter = ref('');
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);
const users = ref<User[]>([]);

const userForm = reactive<UserForm>({
  username: '',
  email: '',
  name: '',
  role: 'member',
  department: '',
  phone: '',
  password: '',
});

const userFormRules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
  ],
  role: [{ required: true, message: '请选择角色', trigger: 'change' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
};

const filteredUsers = computed(() => {
  let filtered = users.value;

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(user =>
      user.name.toLowerCase().includes(query) ||
      user.email.toLowerCase().includes(query)
    );
  }

  if (roleFilter.value) {
    filtered = filtered.filter(user => user.role === roleFilter.value);
  }

  if (statusFilter.value) {
    filtered = filtered.filter(user => user.status === statusFilter.value);
  }

  if (departmentFilter.value) {
    filtered = filtered.filter(user => user.department === departmentFilter.value);
  }

  return filtered;
});

const getRoleType = (role: string) => {
  const types: Record<string, string> = {
    admin: 'danger',
    manager: 'warning',
    member: '',
  };
  return types[role] || '';
};

const getRoleText = (role: string) => {
  const texts: Record<string, string> = {
    admin: '管理员',
    manager: '项目经理',
    member: '成员',
  };
  return texts[role] || role;
};

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    active: 'success',
    inactive: 'info',
  };
  return types[status] || '';
};

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    active: '活跃',
    inactive: '非活跃',
  };
  return texts[status] || status;
};

const formatDate = (date: string) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString('zh-CN');
};

const handleSearch = () => {
  // 搜索逻辑已在computed中处理
};

const handleFilter = () => {
  // 筛选逻辑已在computed中处理
};

const handleSizeChange = (size: number) => {
  pageSize.value = size;
  refreshUsers();
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  refreshUsers();
};

const openUserDetail = (user: User) => {
  selectedUser.value = user;
  showDetailDialog.value = true;
};

const editUser = (user: User) => {
  // 编辑用户逻辑
  console.log('Edit user:', user);
};

const toggleUserStatus = async (user: User) => {
  try {
    const action = user.status === 'active' ? '禁用' : '启用';
    await ElMessageBox.confirm(
      `确定要${action}用户"${user.name}"吗？`,
      `确认${action}`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 这里应该调用API更新用户状态
    user.status = user.status === 'active' ? 'inactive' : 'active';
    ElMessage.success(`用户${action}成功`);
  } catch {
    // 用户取消操作
  }
};

const deleteUser = async (user: User) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户"${user.name}"吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 这里应该调用API删除用户
    console.log('Delete user:', user);
    ElMessage.success('用户删除成功');
    refreshUsers();
  } catch {
    // 用户取消删除
  }
};

const createUser = async () => {
  try {
    // 这里应该调用API创建用户
    console.log('Create user:', userForm);
    ElMessage.success('用户创建成功');
    showCreateDialog.value = false;
    refreshUsers();
  } catch (error) {
    ElMessage.error('用户创建失败');
  }
};

const refreshUsers = async () => {
  try {
    // 模拟数据
    const mockUsers: User[] = [
      {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        name: '管理员',
        role: 'admin',
        department: '技术部',
        phone: '13800138000',
        status: 'active',
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
      {
        id: 2,
        username: 'zhangsan',
        email: '<EMAIL>',
        name: '张三',
        role: 'manager',
        department: '技术部',
        phone: '13800138001',
        status: 'active',
        createdAt: '2024-01-02',
        updatedAt: '2024-01-02',
      },
      {
        id: 3,
        username: 'lisi',
        email: '<EMAIL>',
        name: '李四',
        role: 'member',
        department: '产品部',
        phone: '13800138002',
        status: 'active',
        createdAt: '2024-01-03',
        updatedAt: '2024-01-03',
      },
      {
        id: 4,
        username: 'wangwu',
        email: '<EMAIL>',
        name: '王五',
        role: 'member',
        department: '设计部',
        phone: '13800138003',
        status: 'inactive',
        createdAt: '2024-01-04',
        updatedAt: '2024-01-04',
      },
    ];

    users.value = mockUsers;
    total.value = mockUsers.length;
  } catch (error) {
    ElMessage.error('加载用户失败');
  }
};

onMounted(() => {
  refreshUsers();
});
</script>

<style scoped lang="less">
.personnel {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
    }

    .header-actions {
      display: flex;
      gap: 10px;
    }
  }

  .search-bar {
    margin-bottom: 20px;
  }

  .pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}

.user-detail {
  .user-info {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #ebeef5;

    .info-content {
      flex: 1;

      h3 {
        margin: 0 0 8px 0;
        color: #303133;
      }

      p {
        margin: 0 0 12px 0;
        color: #606266;
      }

      .tags {
        display: flex;
        gap: 8px;
      }
    }
  }
}

@media (max-width: 768px) {
  .personnel {
    .search-bar .el-row {
      flex-direction: column;
      gap: 10px;
    }
  }

  .user-detail .user-info {
    flex-direction: column;
    text-align: center;
  }
}
</style>
