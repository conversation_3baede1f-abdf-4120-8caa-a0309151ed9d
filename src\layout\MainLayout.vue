<template>
  <div class="main-layout">
    <!-- 顶部导航栏 -->
    <header class="top-nav">
      <div class="system-info">
        <h1>项目管理系统</h1>
      </div>

      <!-- 登录/退出按钮 -->
      <div class="auth-container">
        <span v-if="userInfo" class="username">{{ userInfo.username }}</span>
        <button v-if="!userInfo" @click="showLoginModal = true" class="auth-btn">登录</button>
        <button v-else @click="handleLogout" class="auth-btn logout-btn">退出登录</button>
      </div>
    </header>

    <!-- 登录模态框 -->
    <LoginModal
      v-if="showLoginModal"
      @close="showLoginModal = false"
      @login-success="handleLoginSuccess"
    />

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧菜单 -->
      <aside class="sidebar">
        <nav class="sidebar-nav">
          <ul class="menu-list">
            <li v-for="item in menuItems" :key="item.path" class="menu-item">
              <a
                href="javascript:void(0)"
                :class="{
                  'menu-link': true,
                  'router-link-exact-active': $route.path === item.path,
                }"
                @click="handleMenuClick(item.path)"
              >
                {{ item.name }}
              </a>
            </li>
          </ul>
        </nav>

        <!-- 时间显示在菜单底部 -->
        <div class="sidebar-footer">
          <div class="current-time">
            <span class="time-label">当前时间</span>
            <span class="time-value">{{ currentTime }}</span>
          </div>
        </div>
      </aside>

      <!-- 右侧内容 -->
      <main class="content">
        <router-view></router-view>
      </main>
    </div>

    <!-- 底部固定信息 -->
    <footer class="footer">
      <p>© 2025 jooviyo 版权所有</p>
    </footer>

    <!-- 权限调试组件（仅开发环境） -->
    <PermissionDebug v-if="isDevelopment" />
  </div>
</template>
<script>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { logout } from '@/utils/api';
import { showLoginSuccess, showLogoutSuccess, showWarning, showErrorToast } from '@/utils/toast';
import LoginModal from '@/views/LoginModal.vue';
import PermissionDebug from '@/components/PermissionDebug.vue';

export default {
  name: 'MainLayout',
  components: { LoginModal, PermissionDebug },
  setup() {
    const router = useRouter();
    const store = useStore();

    const currentTime = ref('');
    const timer = ref(null);
    const showLoginModal = ref(false);
    const userInfo = ref(null);
    const isDevelopment = process.env.NODE_ENV === 'development';

    const allMenuItems = [
      {
        path: '/dashboard',
        name: '仪表盘',
        requiresAuth: false, // 所有用户都可以访问
        permission: 'dashboard'
      },
      {
        path: '/projects',
        name: '项目管理',
        requiresAuth: true, // 需要登录
        permission: 'projects'
      },
      {
        path: '/tasks',
        name: '任务管理',
        requiresAuth: true, // 需要登录
        permission: 'tasks'
      },
      {
        path: '/UserDashboard',
        name: '个人看板',
        requiresAuth: true, // 需要登录
        permission: 'tasks'
      },
      {
        path: '/users',
        name: '人员管理',
        requiresAuth: true, // 需要登录
        permission: 'personnel',
        adminOnly: true // 只有管理员可以访问
      },
    ];

    const menuItems = computed(() => {
      // 未登录用户只显示仪表盘
      if (!userInfo.value) {
        return allMenuItems.filter(item => !item.requiresAuth);
      }

      // 已登录用户根据权限显示菜单
      return allMenuItems.filter(item => {
        // 仪表盘所有人都可以访问
        if (!item.requiresAuth) {
          return true;
        }

        // 人员管理：只有管理员和项目经理可以看到
        if (item.adminOnly) {
          return store.getters.canAccessPersonnel;
        }

        // 其他功能：检查具体权限
        if (item.permission) {
          return store.getters.hasPermission(item.permission);
        }

        // 默认需要登录
        return !!userInfo.value;
      });
    });

    const updateTime = () => {
      const now = new Date();
      currentTime.value = now.toLocaleString('zh-CN');
    };

    const handleLogout = async () => {
      try {
        await logout();
        // 清除 Vuex 状态
        store.commit('clearUser');
        // 更新本地用户信息
        userInfo.value = null;
        showLogoutSuccess();
        // 跳转到仪表盘（未登录状态）
        router.push('/dashboard');
      } catch (error) {
        console.error('退出登录失败:', error);
        // 即使API调用失败，也要清除本地状态
        store.commit('clearUser');
        userInfo.value = null;
        showLogoutSuccess();
        router.push('/dashboard');
      }
    };

    const handleMenuClick = path => {
      const menuItem = allMenuItems.find(item => item.path === path);

      // 仪表盘所有人都可以访问
      if (path === '/dashboard') {
        router.push(path);
        return;
      }

      // 其他功能需要登录
      if (menuItem && menuItem.requiresAuth && !userInfo.value) {
        showWarning('请先登录', '请先登录后再访问此功能');
        showLoginModal.value = true;
        return;
      }

      // 已登录用户的权限检查
      if (menuItem && userInfo.value) {
        // 人员管理：只有管理员和项目经理可以访问
        if (menuItem.adminOnly) {
          if (!store.getters.canAccessPersonnel) {
            showErrorToast(
              '权限不足',
              '只有管理员和项目经理可以访问人员管理功能'
            );
            return;
          }
        }

        // 项目管理和任务管理：登录用户都可以访问
        else if (menuItem.permission && menuItem.permission !== 'dashboard') {
          if (!store.getters.hasPermission(menuItem.permission)) {
            showErrorToast(
              '权限不足',
              '您没有权限访问此功能，请联系管理员'
            );
            return;
          }
        }
      }

      router.push(path);
    };

    onMounted(() => {
      updateTime();
      timer.value = setInterval(updateTime, 1000);

      // 获取用户信息
      userInfo.value = store.state.userInfo;
    });

    onUnmounted(() => {
      if (timer.value) {
        clearInterval(timer.value);
      }
    });

    const handleLoginSuccess = () => {
      showLoginModal.value = false;

      // 获取最新的用户信息
      userInfo.value = store.state.userInfo;

      // 显示美化的登录成功提示
      showLoginSuccess(userInfo.value?.username || userInfo.value?.name);

      // 登录成功后跳转到仪表盘
      setTimeout(() => {
        router.push('/dashboard');
      }, 1500); // 延迟1.5秒让用户看到成功消息
    };

    return {
      currentTime,
      userInfo,
      menuItems,
      showLoginModal,
      isDevelopment,
      updateTime,
      handleLogout,
      handleLoginSuccess,
      handleMenuClick,
    };
  },
};
</script>

<style scoped>
.main-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(to bottom, #0f0f23, #05051a);
  color: #00ffff;
}

/* 固定顶部导航 */
.top-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 2rem;
  height: 60px;
  background: linear-gradient(135deg, #46597e 0%, #3a4a6b 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  z-index: 100;
  backdrop-filter: blur(10px);
}

.system-info h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.5);
  letter-spacing: 0.5px;
}

.auth-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.username {
  color: #00ffff;
  font-weight: 500;
  font-size: 0.95rem;
}

.auth-btn {
  padding: 0.5rem 1.25rem;
  border: none;
  border-radius: 20px;
  background: linear-gradient(135deg, #00ffff 0%, #00bfff 100%);
  color: #0f0f23;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 255, 255, 0.3);
}

.auth-btn:hover {
  background: linear-gradient(135deg, #00dddd 0%, #00aaff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 255, 255, 0.4);
}

.logout-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  box-shadow: 0 2px 6px rgba(255, 107, 107, 0.3);
}

.logout-btn:hover {
  background: linear-gradient(135deg, #ff5252 0%, #e53935 100%);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

/* 主要内容区域 - 为固定导航留出空间 */
.main-content {
  flex: 1;
  display: flex;
  padding: 2rem;
  gap: 2rem;
  margin-top: 60px; /* 为固定导航留出空间 */
}

/* 侧边栏 */
.sidebar {
  width: 200px;
  background: linear-gradient(135deg, #1a1a40 0%, #151530 100%);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-nav {
  flex: 1;
  padding: 1.5rem 0;
}

.menu-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu-item {
  margin: 0.25rem 0;
}

.menu-link {
  display: block;
  padding: 0.875rem 1.5rem;
  color: #00ffff;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  position: relative;
}

.menu-link:hover {
  background: rgba(0, 255, 255, 0.1);
  border-left-color: #00ffff;
  transform: translateX(4px);
}

.menu-link.router-link-exact-active {
  background: linear-gradient(90deg, rgba(0, 255, 255, 0.2) 0%, rgba(0, 255, 255, 0.05) 100%);
  border-left-color: #00ffff;
  color: #ffffff;
  box-shadow: inset 0 0 10px rgba(0, 255, 255, 0.1);
}

/* 侧边栏底部时间显示 */
.sidebar-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(0, 255, 255, 0.2);
  background: rgba(0, 0, 0, 0.2);
}

.current-time {
  text-align: center;
}

.time-label {
  display: block;
  font-size: 0.75rem;
  color: #7ee7ff;
  margin-bottom: 0.25rem;
  opacity: 0.8;
}

.time-value {
  display: block;
  font-size: 0.85rem;
  color: #00ffff;
  font-weight: 500;
  font-family: 'Courier New', monospace;
}

/* 主内容区域 */
.content {
  flex: 1;
  background: linear-gradient(135deg, #1a1a40 0%, #151530 100%);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
  overflow-y: auto;
}

/* 底部信息 */
.footer {
  text-align: center;
  padding: 1rem;
  background: linear-gradient(135deg, #1a1a40 0%, #151530 100%);
  color: #7ee7ff;
  font-size: 0.85rem;
  opacity: 0.8;
  border-top: 1px solid rgba(0, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-nav {
    padding: 0.5rem 1rem;
    height: 50px;
  }

  .system-info h1 {
    font-size: 1.25rem;
  }

  .main-content {
    margin-top: 50px;
    padding: 1rem;
    gap: 1rem;
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    order: 2;
  }

  .content {
    order: 1;
  }

  .auth-btn {
    padding: 0.4rem 1rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .top-nav {
    padding: 0.5rem;
  }

  .system-info h1 {
    font-size: 1.1rem;
  }

  .main-content {
    padding: 0.5rem;
  }

  .sidebar {
    border-radius: 8px;
  }

  .content {
    border-radius: 8px;
    padding: 1rem;
  }
}


</style>
