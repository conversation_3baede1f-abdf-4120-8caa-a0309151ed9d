<template>
  <div class="tasks">
    <div class="page-header">
      <h2>任务管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建任务
        </el-button>
        <el-button @click="refreshTasks">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchQuery"
            placeholder="搜索任务标题"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="任务状态" @change="handleFilter">
            <el-option label="全部" value="" />
            <el-option label="待办" value="todo" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="审核中" value="review" />
            <el-option label="已完成" value="done" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="priorityFilter" placeholder="优先级" @change="handleFilter">
            <el-option label="全部" value="" />
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="projectFilter" placeholder="所属项目" @change="handleFilter">
            <el-option label="全部项目" value="" />
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="assigneeFilter" placeholder="负责人" @change="handleFilter">
            <el-option label="全部人员" value="" />
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            />
          </el-select>
        </el-col>
      </el-row>
    </div>

    <!-- 任务表格 -->
    <el-table
      :data="filteredTasks"
      style="width: 100%"
      @row-click="openTaskDetail"
    >
      <el-table-column prop="title" label="任务标题" min-width="200">
        <template #default="{ row }">
          <div class="task-title-cell">
            <span>{{ row.title }}</span>
            <el-tag
              v-if="isOverdue(row.dueDate)"
              type="danger"
              size="small"
              class="overdue-tag"
            >
              逾期
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="project.name" label="所属项目" width="150" />

      <el-table-column prop="assignee.name" label="负责人" width="120">
        <template #default="{ row }">
          <div v-if="row.assignee" class="assignee-cell">
            <el-avatar :size="24" :src="row.assignee.avatar">
              {{ row.assignee.name?.charAt(0) }}
            </el-avatar>
            <span>{{ row.assignee.name }}</span>
          </div>
          <span v-else class="text-placeholder">未分配</span>
        </template>
      </el-table-column>

      <el-table-column prop="priority" label="优先级" width="100">
        <template #default="{ row }">
          <el-tag :type="getPriorityType(row.priority)" size="small">
            {{ getPriorityText(row.priority) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)" size="small">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="dueDate" label="截止日期" width="120">
        <template #default="{ row }">
          <span :class="{ 'text-danger': isOverdue(row.dueDate) }">
            {{ formatDate(row.dueDate) }}
          </span>
        </template>
      </el-table-column>

      <el-table-column prop="createdAt" label="创建时间" width="120">
        <template #default="{ row }">
          {{ formatDate(row.createdAt) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click.stop="editTask(row)">
            编辑
          </el-button>
          <el-button size="small" type="danger" @click.stop="deleteTask(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 创建任务对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建新任务"
      width="600px"
    >
      <el-form
        ref="taskFormRef"
        :model="taskForm"
        :rules="taskFormRules"
        label-width="100px"
      >
        <el-form-item label="任务标题" prop="title">
          <el-input v-model="taskForm.title" placeholder="请输入任务标题" />
        </el-form-item>

        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="taskForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述"
          />
        </el-form-item>

        <el-form-item label="所属项目" prop="projectId">
          <el-select v-model="taskForm.projectId" placeholder="请选择项目">
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="任务状态" prop="status">
          <el-select v-model="taskForm.status" placeholder="请选择状态">
            <el-option label="待办" value="todo" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="审核中" value="review" />
            <el-option label="已完成" value="done" />
          </el-select>
        </el-form-item>

        <el-form-item label="优先级" prop="priority">
          <el-select v-model="taskForm.priority" placeholder="请选择优先级">
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>

        <el-form-item label="负责人" prop="assigneeId">
          <el-select v-model="taskForm.assigneeId" placeholder="请选择负责人">
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="截止日期" prop="dueDate">
          <el-date-picker
            v-model="taskForm.dueDate"
            type="date"
            placeholder="请选择截止日期"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="标签" prop="tags">
          <el-select
            v-model="taskForm.tags"
            multiple
            filterable
            allow-create
            placeholder="请选择或输入标签"
            style="width: 100%"
          >
            <el-option
              v-for="tag in commonTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createTask">确定</el-button>
      </template>
    </el-dialog>

    <!-- 任务详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="任务详情"
      width="800px"
    >
      <div v-if="selectedTask" class="task-detail">
        <h3>{{ selectedTask.title }}</h3>
        <p>{{ selectedTask.description }}</p>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Plus,
  Refresh,
  Search,
} from '@element-plus/icons-vue';
import type { Task, Project, User, TaskForm } from '@/types';

const showCreateDialog = ref(false);
const showDetailDialog = ref(false);
const selectedTask = ref<Task | null>(null);
const searchQuery = ref('');
const statusFilter = ref('');
const priorityFilter = ref('');
const projectFilter = ref('');
const assigneeFilter = ref('');
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);
const tasks = ref<Task[]>([]);
const projects = ref<Project[]>([]);
const users = ref<User[]>([]);
const commonTags = ref(['前端', '后端', '设计', '测试', '文档', '优化']);

const taskForm = reactive<TaskForm>({
  title: '',
  description: '',
  status: 'todo',
  priority: 'medium',
  projectId: 0,
  assigneeId: undefined,
  dueDate: '',
  tags: [],
});

const taskFormRules = {
  title: [{ required: true, message: '请输入任务标题', trigger: 'blur' }],
  projectId: [{ required: true, message: '请选择项目', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
};

const filteredTasks = computed(() => {
  let filtered = tasks.value;

  if (searchQuery.value) {
    filtered = filtered.filter(task =>
      task.title.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  if (statusFilter.value) {
    filtered = filtered.filter(task => task.status === statusFilter.value);
  }

  if (priorityFilter.value) {
    filtered = filtered.filter(task => task.priority === priorityFilter.value);
  }

  if (projectFilter.value) {
    filtered = filtered.filter(task => task.projectId === Number(projectFilter.value));
  }

  if (assigneeFilter.value) {
    filtered = filtered.filter(task => task.assigneeId === Number(assigneeFilter.value));
  }

  return filtered;
});

const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    low: '',
    medium: 'warning',
    high: 'danger',
    urgent: 'danger',
  };
  return types[priority] || '';
};

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急',
  };
  return texts[priority] || priority;
};

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    todo: 'info',
    in_progress: 'warning',
    review: 'primary',
    done: 'success',
  };
  return types[status] || '';
};

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    todo: '待办',
    in_progress: '进行中',
    review: '审核中',
    done: '已完成',
  };
  return texts[status] || status;
};

const formatDate = (date: string) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString('zh-CN');
};

const isOverdue = (dueDate: string) => {
  if (!dueDate) return false;
  return new Date(dueDate) < new Date();
};

const handleSearch = () => {
  // 搜索逻辑已在computed中处理
};

const handleFilter = () => {
  // 筛选逻辑已在computed中处理
};

const handleSizeChange = (size: number) => {
  pageSize.value = size;
  refreshTasks();
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  refreshTasks();
};

const openTaskDetail = (task: Task) => {
  selectedTask.value = task;
  showDetailDialog.value = true;
};

const editTask = (task: Task) => {
  // 编辑任务逻辑
  console.log('Edit task:', task);
};

const deleteTask = async (task: Task) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务"${task.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 这里应该调用API删除任务
    console.log('Delete task:', task);
    ElMessage.success('任务删除成功');
    refreshTasks();
  } catch {
    // 用户取消删除
  }
};

const createTask = async () => {
  try {
    // 这里应该调用API创建任务
    console.log('Create task:', taskForm);
    ElMessage.success('任务创建成功');
    showCreateDialog.value = false;
    refreshTasks();
  } catch (error) {
    ElMessage.error('任务创建失败');
  }
};

const refreshTasks = async () => {
  try {
    // 模拟数据
    const mockTasks: Task[] = [
      {
        id: 1,
        title: '设计用户界面',
        description: '完成项目管理系统的用户界面设计',
        status: 'in_progress',
        priority: 'high',
        projectId: 1,
        project: { id: 1, name: '项目管理系统' } as Project,
        assigneeId: 1,
        assignee: { id: 1, name: '张三' } as User,
        creatorId: 1,
        creator: {} as User,
        dueDate: '2024-01-20',
        tags: ['设计', '前端'],
        attachments: [],
        createdAt: '2024-01-01',
        updatedAt: '',
      },
      {
        id: 2,
        title: '实现API接口',
        description: '开发后端API接口',
        status: 'todo',
        priority: 'medium',
        projectId: 1,
        project: { id: 1, name: '项目管理系统' } as Project,
        assigneeId: 2,
        assignee: { id: 2, name: '李四' } as User,
        creatorId: 1,
        creator: {} as User,
        dueDate: '2024-01-25',
        tags: ['后端', 'API'],
        attachments: [],
        createdAt: '2024-01-02',
        updatedAt: '',
      },
    ];

    tasks.value = mockTasks;
    total.value = mockTasks.length;
  } catch (error) {
    ElMessage.error('加载任务失败');
  }
};

onMounted(() => {
  refreshTasks();
  // 加载项目和用户数据
  projects.value = [
    { id: 1, name: '项目管理系统' } as Project,
    { id: 2, name: '电商平台' } as Project,
  ];
  users.value = [
    { id: 1, name: '张三' } as User,
    { id: 2, name: '李四' } as User,
    { id: 3, name: '王五' } as User,
  ];
});
</script>

<style scoped lang="less">
.tasks {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
    }

    .header-actions {
      display: flex;
      gap: 10px;
    }
  }

  .search-bar {
    margin-bottom: 20px;
  }

  .task-title-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .overdue-tag {
      flex-shrink: 0;
    }
  }

  .assignee-cell {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .text-placeholder {
    color: #c0c4cc;
    font-style: italic;
  }

  .text-danger {
    color: #f56c6c;
  }

  .pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}

.task-detail {
  h3 {
    margin-top: 0;
    color: #303133;
  }

  p {
    color: #606266;
    line-height: 1.6;
  }
}

@media (max-width: 768px) {
  .tasks {
    .search-bar .el-row {
      flex-direction: column;
      gap: 10px;
    }
  }
}
</style>
