<template>
  <div class="projects">
    <div class="page-header">
      <h2>项目管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          新建项目
        </el-button>
        <el-button @click="refreshProjects">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchQuery"
            placeholder="搜索项目名称"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="项目状态" @change="handleFilter">
            <el-option label="全部" value="" />
            <el-option label="计划中" value="planning" />
            <el-option label="进行中" value="active" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="priorityFilter" placeholder="优先级" @change="handleFilter">
            <el-option label="全部" value="" />
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-col>
      </el-row>
    </div>

    <!-- 项目列表 -->
    <div class="projects-grid">
      <el-card
        v-for="project in filteredProjects"
        :key="project.id"
        class="project-card"
        @click="openProjectDetail(project)"
      >
        <div class="project-header">
          <div class="project-title">
            <h3>{{ project.name }}</h3>
            <el-dropdown @command="handleProjectAction">
              <el-icon class="project-menu"><MoreFilled /></el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{ action: 'edit', project }">
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'delete', project }">
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>

          <div class="project-meta">
            <el-tag :type="getStatusType(project.status)">
              {{ getStatusText(project.status) }}
            </el-tag>
            <el-tag :type="getPriorityType(project.priority)" size="small">
              {{ getPriorityText(project.priority) }}
            </el-tag>
          </div>
        </div>

        <div class="project-description">
          {{ project.description }}
        </div>

        <div class="project-progress">
          <div class="progress-label">
            <span>进度</span>
            <span>{{ project.progress }}%</span>
          </div>
          <el-progress :percentage="project.progress" :show-text="false" />
        </div>

        <div class="project-info">
          <div class="info-item">
            <el-icon><Calendar /></el-icon>
            <span>{{ formatDateRange(project.startDate, project.endDate) }}</span>
          </div>
          <div class="info-item">
            <el-icon><User /></el-icon>
            <span>{{ project.manager?.name }}</span>
          </div>
          <div class="info-item">
            <el-icon><UserFilled /></el-icon>
            <span>{{ project.members?.length || 0 }} 人</span>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[12, 24, 48]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 创建项目对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      title="创建新项目"
      width="600px"
    >
      <el-form
        ref="projectFormRef"
        :model="projectForm"
        :rules="projectFormRules"
        label-width="100px"
      >
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="projectForm.name" placeholder="请输入项目名称" />
        </el-form-item>

        <el-form-item label="项目描述" prop="description">
          <el-input
            v-model="projectForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入项目描述"
          />
        </el-form-item>

        <el-form-item label="项目状态" prop="status">
          <el-select v-model="projectForm.status" placeholder="请选择状态">
            <el-option label="计划中" value="planning" />
            <el-option label="进行中" value="active" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>

        <el-form-item label="优先级" prop="priority">
          <el-select v-model="projectForm.priority" placeholder="请选择优先级">
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>

        <el-form-item label="开始日期" prop="startDate">
          <el-date-picker
            v-model="projectForm.startDate"
            type="date"
            placeholder="请选择开始日期"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="结束日期" prop="endDate">
          <el-date-picker
            v-model="projectForm.endDate"
            type="date"
            placeholder="请选择结束日期"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="项目经理" prop="managerId">
          <el-select v-model="projectForm.managerId" placeholder="请选择项目经理">
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="团队成员" prop="memberIds">
          <el-select
            v-model="projectForm.memberIds"
            multiple
            placeholder="请选择团队成员"
            style="width: 100%"
          >
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createProject">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Plus,
  Refresh,
  Search,
  MoreFilled,
  Calendar,
  User,
  UserFilled,
} from '@element-plus/icons-vue';
import type { Project, User as UserType, ProjectForm } from '@/types';

const showCreateDialog = ref(false);
const searchQuery = ref('');
const statusFilter = ref('');
const priorityFilter = ref('');
const currentPage = ref(1);
const pageSize = ref(12);
const total = ref(0);
const projects = ref<Project[]>([]);
const users = ref<UserType[]>([]);

const projectForm = reactive<ProjectForm>({
  name: '',
  description: '',
  status: 'planning',
  priority: 'medium',
  startDate: '',
  endDate: '',
  managerId: 0,
  memberIds: [],
});

const projectFormRules = {
  name: [{ required: true, message: '请输入项目名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入项目描述', trigger: 'blur' }],
  status: [{ required: true, message: '请选择项目状态', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  startDate: [{ required: true, message: '请选择开始日期', trigger: 'change' }],
  endDate: [{ required: true, message: '请选择结束日期', trigger: 'change' }],
  managerId: [{ required: true, message: '请选择项目经理', trigger: 'change' }],
};

const filteredProjects = computed(() => {
  let filtered = projects.value;

  if (searchQuery.value) {
    filtered = filtered.filter(project =>
      project.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    );
  }

  if (statusFilter.value) {
    filtered = filtered.filter(project => project.status === statusFilter.value);
  }

  if (priorityFilter.value) {
    filtered = filtered.filter(project => project.priority === priorityFilter.value);
  }

  return filtered;
});

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    planning: 'info',
    active: 'warning',
    completed: 'success',
    cancelled: 'danger',
  };
  return types[status] || '';
};

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    planning: '计划中',
    active: '进行中',
    completed: '已完成',
    cancelled: '已取消',
  };
  return texts[status] || status;
};

const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    low: '',
    medium: 'warning',
    high: 'danger',
    urgent: 'danger',
  };
  return types[priority] || '';
};

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急',
  };
  return texts[priority] || priority;
};

const formatDateRange = (startDate: string, endDate: string) => {
  const start = new Date(startDate).toLocaleDateString('zh-CN');
  const end = new Date(endDate).toLocaleDateString('zh-CN');
  return `${start} - ${end}`;
};

const handleSearch = () => {
  // 搜索逻辑已在computed中处理
};

const handleFilter = () => {
  // 筛选逻辑已在computed中处理
};

const handleSizeChange = (size: number) => {
  pageSize.value = size;
  refreshProjects();
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  refreshProjects();
};

const openProjectDetail = (project: Project) => {
  // 跳转到项目详情页
  console.log('Open project detail:', project);
};

const handleProjectAction = (command: { action: string; project: Project }) => {
  const { action, project } = command;
  if (action === 'edit') {
    // 编辑项目
    console.log('Edit project:', project);
  } else if (action === 'delete') {
    // 删除项目
    console.log('Delete project:', project);
  }
};

const createProject = async () => {
  try {
    // 这里应该调用API创建项目
    console.log('Create project:', projectForm);
    ElMessage.success('项目创建成功');
    showCreateDialog.value = false;
    refreshProjects();
  } catch (error) {
    ElMessage.error('项目创建失败');
  }
};

const refreshProjects = async () => {
  try {
    // 模拟数据
    const mockProjects: Project[] = [
      {
        id: 1,
        name: '项目管理系统',
        description: '开发一个现代化的项目管理系统',
        status: 'active',
        priority: 'high',
        startDate: '2024-01-01',
        endDate: '2024-06-30',
        progress: 65,
        managerId: 1,
        manager: { id: 1, name: '张三' } as UserType,
        members: [
          { id: 1, name: '张三' } as UserType,
          { id: 2, name: '李四' } as UserType,
        ],
        createdAt: '',
        updatedAt: '',
      },
      {
        id: 2,
        name: '电商平台',
        description: '构建一个功能完整的电商平台',
        status: 'planning',
        priority: 'medium',
        startDate: '2024-03-01',
        endDate: '2024-12-31',
        progress: 15,
        managerId: 2,
        manager: { id: 2, name: '李四' } as UserType,
        members: [
          { id: 2, name: '李四' } as UserType,
          { id: 3, name: '王五' } as UserType,
        ],
        createdAt: '',
        updatedAt: '',
      },
    ];

    projects.value = mockProjects;
    total.value = mockProjects.length;
  } catch (error) {
    ElMessage.error('加载项目失败');
  }
};

onMounted(() => {
  refreshProjects();
  // 加载用户数据
  users.value = [
    { id: 1, name: '张三' } as UserType,
    { id: 2, name: '李四' } as UserType,
    { id: 3, name: '王五' } as UserType,
  ];
});
</script>

<style scoped lang="less">
.projects {
  padding: 20px;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h2 {
      margin: 0;
    }

    .header-actions {
      display: flex;
      gap: 10px;
    }
  }

  .search-bar {
    margin-bottom: 20px;
  }

  .projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 20px;

    .project-card {
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      .project-header {
        margin-bottom: 12px;

        .project-title {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 8px;

          h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            flex: 1;
          }

          .project-menu {
            color: #909399;
            cursor: pointer;
            padding: 2px;

            &:hover {
              color: #409eff;
            }
          }
        }

        .project-meta {
          display: flex;
          gap: 8px;
        }
      }

      .project-description {
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 16px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }

      .project-progress {
        margin-bottom: 16px;

        .progress-label {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 14px;
          color: #606266;
        }
      }

      .project-info {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .info-item {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 12px;
          color: #909399;

          .el-icon {
            font-size: 14px;
          }
        }
      }
    }
  }

  .pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}

@media (max-width: 768px) {
  .projects {
    .projects-grid {
      grid-template-columns: 1fr;
    }

    .search-bar .el-row {
      flex-direction: column;
      gap: 10px;
    }
  }
}
</style>
