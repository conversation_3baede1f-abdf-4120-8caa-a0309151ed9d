<template>
  <div class="permission-debug" v-if="showDebug">
    <div class="debug-panel">
      <h4>权限调试信息</h4>
      <div class="debug-info">
        <div class="info-item">
          <strong>登录状态:</strong> 
          <span :class="isAuthenticated ? 'status-success' : 'status-error'">
            {{ isAuthenticated ? '已登录' : '未登录' }}
          </span>
        </div>
        
        <div class="info-item" v-if="isAuthenticated">
          <strong>用户信息:</strong> 
          <span>{{ currentUser?.username || currentUser?.name || '未知' }}</span>
        </div>
        
        <div class="info-item">
          <strong>用户角色:</strong> 
          <span :class="getRoleClass(userRole)">{{ getRoleText(userRole) }}</span>
        </div>
        
        <div class="info-item">
          <strong>权限列表:</strong>
          <div class="permissions-list">
            <span 
              v-for="permission in permissions" 
              :key="permission" 
              class="permission-tag"
            >
              {{ permission }}
            </span>
            <span v-if="permissions.length === 0" class="no-permissions">无权限</span>
          </div>
        </div>
        
        <div class="info-item">
          <strong>功能权限:</strong>
          <div class="feature-permissions">
            <div class="feature-item">
              <span>仪表盘:</span>
              <span :class="canAccessDashboard ? 'status-success' : 'status-error'">
                {{ canAccessDashboard ? '✓' : '✗' }}
              </span>
            </div>
            <div class="feature-item">
              <span>项目管理:</span>
              <span :class="canAccessProjects ? 'status-success' : 'status-error'">
                {{ canAccessProjects ? '✓' : '✗' }}
              </span>
            </div>
            <div class="feature-item">
              <span>任务管理:</span>
              <span :class="canAccessTasks ? 'status-success' : 'status-error'">
                {{ canAccessTasks ? '✓' : '✗' }}
              </span>
            </div>
            <div class="feature-item">
              <span>人员管理:</span>
              <span :class="canAccessPersonnel ? 'status-success' : 'status-error'">
                {{ canAccessPersonnel ? '✓' : '✗' }}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <button @click="toggleDebug" class="close-debug">关闭调试</button>
    </div>
  </div>
  
  <!-- 调试按钮 -->
  <button 
    v-if="!showDebug" 
    @click="toggleDebug" 
    class="debug-toggle"
    title="显示权限调试信息"
  >
    🔍
  </button>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useStore } from 'vuex';

const store = useStore();
const showDebug = ref(false);

const isAuthenticated = computed(() => store.getters.isAuthenticated);
const currentUser = computed(() => store.getters.currentUser);
const userRole = computed(() => store.getters.userRole);
const permissions = computed(() => store.getters.permissions);

const canAccessDashboard = computed(() => store.getters.canAccessDashboard);
const canAccessProjects = computed(() => store.getters.canAccessProjects);
const canAccessTasks = computed(() => store.getters.canAccessTasks);
const canAccessPersonnel = computed(() => store.getters.canAccessPersonnel);

const toggleDebug = () => {
  showDebug.value = !showDebug.value;
};

const getRoleClass = (role) => {
  const roleClasses = {
    admin: 'role-admin',
    manager: 'role-manager',
    project_manager: 'role-manager',
    user: 'role-user',
    guest: 'role-guest'
  };
  return roleClasses[role] || 'role-guest';
};

const getRoleText = (role) => {
  const roleTexts = {
    admin: '管理员',
    manager: '经理',
    project_manager: '项目经理',
    user: '普通用户',
    guest: '访客'
  };
  return roleTexts[role] || '访客';
};
</script>

<style scoped>
.permission-debug {
  position: fixed;
  top: 100px;
  right: 20px;
  z-index: 1001;
}

.debug-panel {
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  min-width: 300px;
  max-width: 400px;
  font-size: 0.85rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.debug-panel h4 {
  margin: 0 0 1rem;
  color: #00ffff;
  text-align: center;
}

.info-item {
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.info-item strong {
  color: #00ffff;
}

.status-success {
  color: #10b981;
  font-weight: bold;
}

.status-error {
  color: #ef4444;
  font-weight: bold;
}

.role-admin {
  color: #f59e0b;
  font-weight: bold;
}

.role-manager {
  color: #8b5cf6;
  font-weight: bold;
}

.role-user {
  color: #06b6d4;
  font-weight: bold;
}

.role-guest {
  color: #6b7280;
  font-weight: bold;
}

.permissions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-top: 0.25rem;
}

.permission-tag {
  background: #374151;
  color: #00ffff;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
}

.no-permissions {
  color: #6b7280;
  font-style: italic;
}

.feature-permissions {
  margin-top: 0.25rem;
}

.feature-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.25rem;
}

.close-debug {
  background: #374151;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  width: 100%;
  margin-top: 1rem;
}

.close-debug:hover {
  background: #4b5563;
}

.debug-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: #00ffff;
  color: #0f0f23;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.5rem;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
  transition: all 0.2s ease;
}

.debug-toggle:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 255, 255, 0.4);
}

@media (max-width: 480px) {
  .permission-debug {
    top: 80px;
    right: 10px;
    left: 10px;
  }
  
  .debug-panel {
    min-width: auto;
    max-width: none;
  }
}
</style>
