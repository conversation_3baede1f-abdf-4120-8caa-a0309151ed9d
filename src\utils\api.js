import axios from 'axios';

// 创建axios实例
const apiClient = axios.create({
  baseURL: process.env.VUE_APP_API_URL, // 从环境变量获取API基础URL
  timeout: 10000, // 设置超时时间为10秒
});

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bear<PERSON> ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  response => {
    return response.data;
  },
  error => {
    // 处理全局错误
    if (error.response && error.response.status === 401) {
      // 处理未授权的情况，例如跳转到登录页面
      window.location.href = '/login';
    } else if (error.response && error.response.status === 500) {
      console.error('服务器内部错误:', error);
      alert('服务器内部错误，请稍后再试');
    }
    return Promise.reject(error);
  }
);

// 模拟用户数据
const mockUsers = [
  {
    id: 1,
    username: 'admin',
    password: 'admin123',
    name: '管理员',
    role: 'admin',
    email: '<EMAIL>'
  },
  {
    id: 2,
    username: 'user',
    password: 'user123',
    name: '普通用户',
    role: 'user',
    email: '<EMAIL>'
  },
  {
    id: 3,
    username: 'test',
    password: '123456',
    name: '测试用户',
    role: 'user',
    email: '<EMAIL>'
  }
];

// 模拟登录函数
const mockLogin = (username, password) => {
  return new Promise((resolve, reject) => {
    // 模拟网络延迟
    setTimeout(() => {
      const user = mockUsers.find(u => u.username === username && u.password === password);

      if (user) {
        const token = `mock_token_${user.id}_${Date.now()}`;
        const userInfo = {
          id: user.id,
          username: user.username,
          name: user.name,
          role: user.role,
          email: user.email
        };

        resolve({
          success: true,
          message: '登录成功',
          data: {
            token,
            userInfo
          }
        });
      } else {
        reject({
          response: {
            data: {
              success: false,
              message: '用户名或密码错误'
            }
          }
        });
      }
    }, 800); // 模拟800ms的网络延迟
  });
};

// 用户相关API
export const login = (username, password) => {
  // 如果没有配置API URL或者API服务不可用，使用模拟登录
  if (!process.env.VUE_APP_API_URL || process.env.VUE_APP_API_URL.includes('localhost')) {
    console.log('使用模拟登录功能');
    return mockLogin(username, password);
  }

  // 真实API调用
  return apiClient.post('/users/login', {
    username,
    password,
  });
};

// 模拟登出函数
const mockLogout = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        success: true,
        message: '登出成功'
      });
    }, 300);
  });
};

export const logout = () => {
  // 如果没有配置API URL或者API服务不可用，使用模拟登出
  if (!process.env.VUE_APP_API_URL || process.env.VUE_APP_API_URL.includes('localhost')) {
    console.log('使用模拟登出功能');
    return mockLogout();
  }

  return apiClient.post('/users/logout');
};

// 获取用户信息
export const getUserInfo = () => {
  return apiClient.get('/users/info');
};

// 修改密码
export const changePassword = (oldPassword, newPassword) => {
  return apiClient.post('/users/change-password', {
    oldPassword,
    newPassword,
  });
};
