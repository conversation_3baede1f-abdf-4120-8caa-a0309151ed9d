<template>
  <div class="modal-overlay" @click="handleOverlayClick" @keydown.esc="$emit('close')">
    <div class="modal-content" @click.stop ref="modalContent">
      <!-- 关闭按钮 -->
      <button
        class="close-btn"
        type="button"
        aria-label="关闭登录窗口"
        @click="$emit('close')"
        ref="closeButton"
      >
        <svg class="close-icon" viewBox="0 0 24 24" width="24" height="24">
          <path
            d="M18 6L6 18M6 6l12 12"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>

      <!-- 头部区域 -->
      <div class="modal-header">
        <img src="@/assets/logo.png" alt="系统Logo" class="login-logo" />
        <h2 class="modal-title">用户登录</h2>
        <p class="modal-subtitle">项目管理系统</p>
      </div>

      <!-- 登录表单 -->
      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <label for="username" class="form-label">用户名</label>
          <div class="input-wrapper">
            <input
              id="username"
              ref="usernameInput"
              v-model="loginForm.username"
              type="text"
              class="form-input"
              placeholder="请输入用户名"
              required
              autocomplete="username"
              :disabled="loading"
            />
          </div>
        </div>

        <div class="form-group">
          <label for="password" class="form-label">密码</label>
          <div class="input-wrapper password-wrapper">
            <input
              id="password"
              v-model="loginForm.password"
              :type="showPassword ? 'text' : 'password'"
              class="form-input"
              placeholder="请输入密码"
              required
              autocomplete="current-password"
              :disabled="loading"
            />
            <button
              type="button"
              class="password-toggle"
              @click="togglePasswordVisibility"
              :aria-label="showPassword ? '隐藏密码' : '显示密码'"
              :disabled="loading"
            >
              <svg v-if="showPassword" viewBox="0 0 24 24" width="20" height="20">
                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M1 1l22 22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <svg v-else viewBox="0 0 24 24" width="20" height="20">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
        </div>

        <div class="form-actions">
          <button
            type="submit"
            class="login-btn"
            :disabled="loading || !loginForm.username || !loginForm.password"
          >
            <span v-if="loading" class="loading-spinner"></span>
            <span>{{ loading ? '登录中...' : '登录' }}</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, defineEmits, onMounted, nextTick } from 'vue';
import { useStore } from 'vuex';
import { login } from '@/utils/api';
import { showErrorToast } from '@/utils/toast';

const emit = defineEmits(['close', 'login-success']);

const store = useStore();
const loading = ref(false);
const showPassword = ref(false);

// 模板引用
const modalContent = ref(null);
const usernameInput = ref(null);
const closeButton = ref(null);

const loginForm = reactive({
  username: '',
  password: '',
});

// 处理遮罩层点击
const handleOverlayClick = (event) => {
  if (event.target === event.currentTarget) {
    emit('close');
  }
};

// 切换密码可见性
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
};

// 处理登录
const handleLogin = async () => {
  if (!loginForm.username.trim() || !loginForm.password.trim()) {
    showErrorToast(
      '信息不完整',
      '请输入用户名和密码后再试'
    );
    return;
  }

  loading.value = true;
  const minLoading = new Promise(resolve => setTimeout(resolve, 500)); // 至少loading 500ms

  try {
    const response = await login(loginForm.username.trim(), loginForm.password);
    await minLoading;

    console.log('登录响应:', response);

    // 检查响应格式，适配不同的API响应结构
    let success = false;
    let token = null;
    let userInfo = null;
    let message = '';

    if (response) {
      // 情况1: 响应格式为 { success: true, data: { token, userInfo } }
      if (response.success === true && response.data) {
        success = true;
        token = response.data.token;
        userInfo = response.data.userInfo || response.data.user;
        message = response.message || '登录成功';
      }
      // 情况2: 响应格式为 { code: 200, data: { token, userInfo } }
      else if ((response.code === 200 || response.status === 200) && response.data) {
        success = true;
        token = response.data.token;
        userInfo = response.data.userInfo || response.data.user;
        message = response.message || '登录成功';
      }
      // 情况3: 直接返回 { token, userInfo }
      else if (response.token) {
        success = true;
        token = response.token;
        userInfo = response.userInfo || response.user || response;
        message = '登录成功';
      }
      // 情况4: 响应状态码为200但没有明确的success字段
      else if (response.status === 200 || (!response.hasOwnProperty('success') && !response.hasOwnProperty('code'))) {
        // 如果响应状态是200且包含看起来像用户信息的数据，认为是成功
        if (response.data?.token || response.token || (response.data && Object.keys(response.data).length > 0)) {
          success = true;
          token = response.data?.token || response.token;
          userInfo = response.data?.userInfo || response.data?.user || response.data || response.userInfo || response.user;
          message = response.message || response.msg || '登录成功';
        }
      }
      // 情况5: 明确的失败响应
      else if (response.success === false || (response.code && response.code !== 200)) {
        message = response.message || response.msg || '登录失败';
      }
    }

    if (success && token) {
      // 更新 Vuex store
      store.commit('setToken', token);
      store.commit('setUser', userInfo);

      // 发出登录成功事件（不在这里显示成功消息，由父组件处理）
      emit('login-success');

      // 清空表单
      loginForm.username = '';
      loginForm.password = '';
    } else {
      showErrorToast(
        '登录失败',
        message || '请检查用户名和密码是否正确'
      );
    }
  } catch (error) {
    await minLoading;
    console.error('登录失败:', error);

    let errorTitle = '登录失败';
    let errorDescription = '请检查网络连接';

    if (error.response) {
      // 服务器响应了错误状态码
      const { status, data } = error.response;
      if (status === 401) {
        errorTitle = '认证失败';
        errorDescription = '用户名或密码错误，请重新输入';
      } else if (status === 403) {
        errorTitle = '账号被禁用';
        errorDescription = '您的账号已被禁用，请联系管理员';
      } else if (status === 500) {
        errorTitle = '服务器错误';
        errorDescription = '服务器内部错误，请稍后重试';
      } else if (data?.message || data?.msg) {
        errorDescription = data.message || data.msg;
      } else {
        errorDescription = `请求失败，状态码: ${status}`;
      }
    } else if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
      errorTitle = '网络错误';
      errorDescription = '网络连接失败，请检查网络设置';
    } else if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      errorTitle = '请求超时';
      errorDescription = '网络请求超时，请稍后重试';
    } else if (error.message) {
      errorDescription = error.message;
    }

    showErrorToast(errorTitle, errorDescription);
  } finally {
    loading.value = false;
  }
};

// 组件挂载后设置焦点
onMounted(async () => {
  await nextTick();
  if (usernameInput.value) {
    usernameInput.value.focus();
  }
});
</script>

<style scoped>
/* 模态框遮罩层 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 1rem;
  animation: overlayFadeIn 0.3s ease-out;
}

/* 模态框内容容器 */
.modal-content {
  background: linear-gradient(135deg, #23234b 0%, #1a1a40 100%);
  border-radius: 20px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.6),
    0 0 0 1px rgba(0, 255, 255, 0.1);
  width: 100%;
  max-width: 440px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}
/* 关闭按钮 */
.close-btn {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  color: #00ffff;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(0, 255, 255, 0.1);
  transform: scale(1.1);
}

.close-btn:focus {
  outline: 2px solid #00ffff;
  outline-offset: 2px;
}

.close-icon {
  transition: transform 0.2s ease;
}

.close-btn:hover .close-icon {
  transform: rotate(90deg);
}

/* 头部区域 */
.modal-header {
  text-align: center;
  padding: 2rem 2rem 1.5rem;
  border-bottom: 1px solid rgba(0, 255, 255, 0.1);
}

.login-logo {
  width: 64px;
  height: 64px;
  margin-bottom: 1rem;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
  object-fit: cover;
}

.modal-title {
  margin: 0 0 0.5rem;
  font-size: 1.5rem;
  font-weight: 600;
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
}

.modal-subtitle {
  margin: 0;
  color: #7ee7ff;
  font-size: 0.95rem;
  letter-spacing: 0.5px;
  opacity: 0.9;
}

/* 登录表单 */
.login-form {
  padding: 1.5rem 2rem 2rem;
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  color: #cccccc;
  font-size: 0.9rem;
  font-weight: 500;
}

.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid transparent;
  border-radius: 8px;
  background: linear-gradient(90deg, #2a2a60 0%, #23234b 100%);
  color: #00ffff;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input::placeholder {
  color: #7ee7ff;
  opacity: 0.6;
}

.form-input:focus {
  outline: none;
  border-color: #00ffff;
  box-shadow: 0 0 0 3px rgba(0, 255, 255, 0.1);
  background: linear-gradient(90deg, #2d2d65 0%, #252550 100%);
}

.form-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 密码输入框容器 */
.password-wrapper {
  position: relative;
}

.password-wrapper .form-input {
  padding-right: 3rem; /* 为密码切换按钮留出空间 */
}

.password-toggle {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #00ffff;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
}

.password-toggle:hover {
  background: rgba(0, 255, 255, 0.1);
  color: #ffffff;
}

.password-toggle:focus {
  outline: 2px solid #00ffff;
  outline-offset: 2px;
}

.password-toggle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 表单操作区域 */
.form-actions {
  margin-top: 2rem;
}

.login-btn {
  width: 100%;
  padding: 0.875rem 1rem;
  border: none;
  border-radius: 8px;
  background: linear-gradient(90deg, #00ffff 0%, #00bfff 100%);
  color: #0f0f23;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 255, 255, 0.3);
}

.login-btn:hover:not(:disabled) {
  background: linear-gradient(90deg, #00dddd 0%, #00aaff 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 255, 255, 0.4);
}

.login-btn:active:not(:disabled) {
  transform: translateY(0);
}

.login-btn:disabled {
  background: #666;
  color: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}



/* 加载动画 */
.loading-spinner {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .modal-overlay {
    padding: 0.5rem;
  }

  .modal-content {
    max-width: 100%;
    border-radius: 16px;
  }

  .modal-header {
    padding: 1.5rem 1.5rem 1rem;
  }

  .login-form {
    padding: 1rem 1.5rem 1.5rem;
  }

  .modal-title {
    font-size: 1.25rem;
  }

  .login-logo {
    width: 56px;
    height: 56px;
  }
}

/* 动画定义 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes overlayFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 无障碍支持 */
@media (prefers-reduced-motion: reduce) {
  .modal-content,
  .modal-overlay,
  .close-btn,
  .password-toggle,
  .login-btn,
  .loading-spinner {
    animation: none;
    transition: none;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .modal-content {
    border: 2px solid #00ffff;
  }

  .form-input {
    border: 1px solid #00ffff;
  }

  .form-input:focus {
    border-width: 2px;
  }
}
</style>
