<template>
  <div v-if="!hasPermission" class="permission-denied">
    <div class="permission-content">
      <div class="permission-icon">
        <svg viewBox="0 0 24 24" width="64" height="64">
          <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z" fill="none" stroke="currentColor" stroke-width="2"/>
          <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" fill="none"/>
        </svg>
      </div>
      <h2>权限不足</h2>
      <p>{{ message }}</p>
      <div class="permission-actions">
        <button @click="goBack" class="btn-secondary">返回</button>
        <button @click="goHome" class="btn-primary">回到首页</button>
      </div>
    </div>
  </div>
  <slot v-else></slot>
</template>

<script setup>
import { computed } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';

const props = defineProps({
  permission: {
    type: String,
    default: ''
  },
  role: {
    type: [String, Array],
    default: ''
  },
  message: {
    type: String,
    default: '您没有权限访问此功能，请联系管理员'
  }
});

const store = useStore();
const router = useRouter();

const hasPermission = computed(() => {
  // 如果没有指定权限要求，默认允许访问
  if (!props.permission && !props.role) {
    return true;
  }

  // 人员管理权限检查：必须是管理员或项目经理
  if (props.permission === 'personnel') {
    // 检查是否已登录
    if (!store.getters.isAuthenticated) {
      return false;
    }

    // 检查是否为管理员
    return store.getters.canAccessPersonnel;
  }

  // 其他权限检查
  if (props.permission && props.permission !== 'dashboard') {
    // 非仪表盘功能需要登录
    if (!store.getters.isAuthenticated) {
      return false;
    }

    return store.getters.hasPermission(props.permission);
  }

  // 检查角色权限
  if (props.role) {
    if (!store.getters.isAuthenticated) {
      return false;
    }

    const userRole = store.getters.userRole.toLowerCase();
    const requiredRoles = Array.isArray(props.role)
      ? props.role.map(r => r.toLowerCase())
      : [props.role.toLowerCase()];

    return requiredRoles.includes(userRole);
  }

  return true;
});

const goBack = () => {
  router.go(-1);
};

const goHome = () => {
  router.push('/dashboard');
};
</script>

<style scoped>
.permission-denied {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
}

.permission-content {
  text-align: center;
  max-width: 400px;
}

.permission-icon {
  color: #f59e0b;
  margin-bottom: 1.5rem;
}

.permission-content h2 {
  color: #1f2937;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.permission-content p {
  color: #6b7280;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.permission-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.btn-primary,
.btn-secondary {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #00ffff 0%, #00bfff 100%);
  color: #0f0f23;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #00dddd 0%, #00aaff 100%);
  transform: translateY(-1px);
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background: #4b5563;
  transform: translateY(-1px);
}
</style>
