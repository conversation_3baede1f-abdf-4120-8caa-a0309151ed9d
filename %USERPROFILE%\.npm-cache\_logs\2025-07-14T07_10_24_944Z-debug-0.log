0 verbose cli C:\Program Files\nodejs\node.exe C:\Program Files\nodejs\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.2
2 info using node@v22.16.0
3 silly config load:file:C:\Program Files\nodejs\node_modules\npm\npmrc
4 silly config load:file:D:\车秘系统相关文档\projects\project_manage\client\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:D:\tools\nodejs\node_global\etc\npmrc
7 verbose title npm run serve
8 verbose argv "run" "serve"
9 verbose logfile logs-max:10 dir:D:\车秘系统相关文档\projects\project_manage\client\%USERPROFILE%\.npm-cache\_logs\2025-07-14T07_10_24_944Z-
10 verbose logfile D:\车秘系统相关文档\projects\project_manage\client\%USERPROFILE%\.npm-cache\_logs\2025-07-14T07_10_24_944Z-debug-0.log
11 silly logfile done cleaning log files
