import { createStore } from 'vuex';
import { getUserInfo } from '@/utils/api';

// 根据用户角色获取权限列表
const getUserPermissions = (role) => {
  const rolePermissions = {
    admin: ['dashboard', 'projects', 'tasks', 'personnel', 'settings'],
    manager: ['dashboard', 'projects', 'tasks', 'personnel'],
    project_manager: ['dashboard', 'projects', 'tasks', 'personnel'],
    user: ['dashboard', 'projects', 'tasks'], // 普通用户不包含personnel权限
    guest: ['dashboard'] // 未登录用户只能访问仪表盘
  };

  const normalizedRole = (role || 'guest').toLowerCase(); // 默认为guest而不是user
  return rolePermissions[normalizedRole] || rolePermissions.guest;
};

export default createStore({
  state: {
    userInfo: JSON.parse(localStorage.getItem('userInfo')) || null,
    token: localStorage.getItem('token') || null,
    permissions: JSON.parse(localStorage.getItem('permissions')) || [],
  },

  mutations: {
    setUser(state, userInfo) {
      state.userInfo = userInfo;
      if (userInfo) {
        localStorage.setItem('userInfo', JSON.stringify(userInfo));
        // 根据用户角色设置权限
        const permissions = getUserPermissions(userInfo.role || userInfo.userType);
        state.permissions = permissions;
        localStorage.setItem('permissions', JSON.stringify(permissions));
      } else {
        localStorage.removeItem('userInfo');
        state.permissions = [];
        localStorage.removeItem('permissions');
      }
    },
    setToken(state, token) {
      state.token = token;
      if (token) {
        localStorage.setItem('token', token);
      } else {
        localStorage.removeItem('token');
      }
    },
    setPermissions(state, permissions) {
      state.permissions = permissions;
      localStorage.setItem('permissions', JSON.stringify(permissions));
    },
    clearUser(state) {
      state.userInfo = null;
      state.token = null;
      state.permissions = [];
      localStorage.removeItem('token');
      localStorage.removeItem('userInfo');
      localStorage.removeItem('permissions');
    },
  },

  actions: {
    // 获取用户信息
    async fetchUserInfo({ commit }) {
      try {
        const response = await getUserInfo();
        if (response.success) {
          commit('setUser', response.data);
          return response.data;
        }
        return null;
      } catch (error) {
        console.error('获取用户信息失败:', error);
        return null;
      }
    },

    // 登出操作
    async logout({ commit }) {
      commit('clearUser');
    },
  },

  getters: {
    isAuthenticated: state => !!state.token,
    currentUser: state => state.userInfo,
    userRole: state => state.userInfo?.role || state.userInfo?.userType || 'guest',
    permissions: state => state.permissions,
    hasPermission: state => permission => state.permissions.includes(permission),

    // 人员管理权限：只有管理员和项目经理可以访问
    canAccessPersonnel: state => {
      if (!state.token) return false; // 未登录用户不能访问
      const role = (state.userInfo?.role || state.userInfo?.userType || 'guest').toLowerCase();
      return ['admin', 'manager', 'project_manager'].includes(role);
    },

    // 项目管理权限：需要登录
    canAccessProjects: state => {
      if (!state.token) return false; // 未登录用户不能访问
      return state.permissions.includes('projects');
    },

    // 任务管理权限：需要登录
    canAccessTasks: state => {
      if (!state.token) return false; // 未登录用户不能访问
      return state.permissions.includes('tasks');
    },

    // 仪表盘权限：所有用户都可以访问（包括未登录用户）
    canAccessDashboard: () => true,

    // 检查是否为管理员
    isAdmin: state => {
      if (!state.token) return false;
      const role = (state.userInfo?.role || state.userInfo?.userType || 'guest').toLowerCase();
      return ['admin', 'manager', 'project_manager'].includes(role);
    },
  },
});
