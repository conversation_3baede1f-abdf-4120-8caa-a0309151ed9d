<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card" v-for="stat in stats" :key="stat.key">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon :size="32" :color="stat.color">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 图表区域 -->
    <div class="charts-grid">
      <el-card class="chart-card">
        <template #header>
          <span>项目进度概览</span>
        </template>
        <div class="chart-container">
          <div class="chart-placeholder">项目进度图表</div>
        </div>
      </el-card>

      <el-card class="chart-card">
        <template #header>
          <span>任务状态分布</span>
        </template>
        <div class="chart-container">
          <div class="chart-placeholder">任务状态图表</div>
        </div>
      </el-card>
    </div>

    <!-- 最近任务 -->
    <PermissionGuard
      feature="my-recent-tasks"
      :requiresLogin="true"
      message="请先登录查看您的最近任务"
    >
      <el-card class="recent-tasks">
        <template #header>
          <div class="card-header">
            <span>我的最近任务</span>
            <el-button type="primary" size="small" @click="$router.push('/tasks')">
              查看全部
            </el-button>
          </div>
        </template>
        <el-table :data="recentTasks" style="width: 100%">
          <el-table-column prop="title" label="任务名称" />
          <el-table-column prop="project.name" label="所属项目" />
          <el-table-column prop="priority" label="优先级">
            <template #default="{ row }">
              <el-tag :type="getPriorityType(row.priority)">
                {{ getPriorityText(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="dueDate" label="截止日期" />
        </el-table>
      </el-card>
    </PermissionGuard>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useStore } from 'vuex';
import {
  Document,
  Folder,
  User,
  Clock,
} from '@element-plus/icons-vue';
import type { DashboardStats, Task } from '@/types';
import PermissionGuard from '@/components/PermissionGuard.vue';

const store = useStore();

const dashboardStats = ref<DashboardStats>({
  totalProjects: 0,
  activeProjects: 0,
  completedProjects: 0,
  totalTasks: 0,
  completedTasks: 0,
  overdueTasks: 0,
  myTasks: 0,
  teamMembers: 0,
});

const recentTasks = ref<Task[]>([]);

const stats = computed(() => {
  const baseStats = [
    {
      key: 'projects',
      label: '总项目数',
      value: dashboardStats.value.totalProjects,
      icon: Folder,
      color: '#409EFF',
    },
    {
      key: 'members',
      label: '团队成员',
      value: dashboardStats.value.teamMembers,
      icon: User,
      color: '#E6A23C',
    },
  ];

  // 只有登录用户才能看到个人相关的统计
  if (store.getters.isAuthenticated) {
    baseStats.splice(1, 0, {
      key: 'tasks',
      label: '我的任务',
      value: dashboardStats.value.myTasks,
      icon: Document,
      color: '#67C23A',
    });

    baseStats.push({
      key: 'overdue',
      label: '我的逾期任务',
      value: dashboardStats.value.overdueTasks,
      icon: Clock,
      color: '#F56C6C',
    });
  } else {
    // 未登录用户显示总体统计
    baseStats.splice(1, 0, {
      key: 'total-tasks',
      label: '总任务数',
      value: dashboardStats.value.totalTasks,
      icon: Document,
      color: '#67C23A',
    });
  }

  return baseStats;
});

const getPriorityType = (priority: string) => {
  const types: Record<string, string> = {
    low: '',
    medium: 'warning',
    high: 'danger',
    urgent: 'danger',
  };
  return types[priority] || '';
};

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急',
  };
  return texts[priority] || priority;
};

const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    todo: 'info',
    in_progress: 'warning',
    review: 'primary',
    done: 'success',
  };
  return types[status] || '';
};

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    todo: '待办',
    in_progress: '进行中',
    review: '审核中',
    done: '已完成',
  };
  return texts[status] || status;
};

const loadDashboardData = async () => {
  try {
    // 模拟数据
    dashboardStats.value = {
      totalProjects: 12,
      activeProjects: 8,
      completedProjects: 3,
      totalTasks: 45,
      completedTasks: 28,
      overdueTasks: 3,
      myTasks: 12,
      teamMembers: 15,
    };

    // 模拟最近任务数据
    recentTasks.value = [
      {
        id: 1,
        title: '完成用户界面设计',
        description: '',
        status: 'in_progress',
        priority: 'high',
        projectId: 1,
        project: { id: 1, name: '项目管理系统' } as any,
        creatorId: 1,
        creator: {} as any,
        dueDate: '2024-01-15',
        tags: [],
        attachments: [],
        createdAt: '',
        updatedAt: '',
      },
    ];
  } catch (error) {
    console.error('加载仪表盘数据失败:', error);
  }
};

onMounted(() => {
  loadDashboardData();
});
</script>

<style scoped lang="less">
.dashboard {
  padding: 20px;

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;

    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .stat-icon {
          flex-shrink: 0;
        }

        .stat-info {
          .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
          }

          .stat-label {
            font-size: 14px;
            color: #909399;
            margin-top: 4px;
          }
        }
      }
    }
  }

  .charts-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;

    .chart-card {
      .chart-container {
        height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;

        .chart-placeholder {
          color: #909399;
          font-size: 16px;
        }
      }
    }
  }

  .recent-tasks {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

@media (max-width: 768px) {
  .dashboard {
    .charts-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
