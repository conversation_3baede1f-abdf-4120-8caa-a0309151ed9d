import { ElMessage } from 'element-plus';

// 增强的Toast函数，支持自定义Toast组件
export const showToast = (message, type = 'success', options = {}) => {
  // 如果有自定义Toast组件可用，优先使用
  if (window.$customToast && typeof window.$customToast[type] === 'function') {
    const { title, description, ...restOptions } = options;
    return window.$customToast[type](
      title || message,
      description,
      restOptions
    );
  }

  // 回退到Element Plus的消息提示
  ElMessage({
    message,
    type,
    duration: options.duration || 3000,
  });
};

// 专门用于登录成功的美化提示
export const showLoginSuccess = (username) => {
  if (window.$customToast) {
    return window.$customToast.success(
      '登录成功！',
      `欢迎回来，${username || '用户'}`,
      {
        duration: 4000,
        showProgress: true,
      }
    );
  }

  showToast(`登录成功！欢迎回来，${username || '用户'}`, 'success');
};

// 专门用于登出成功的提示
export const showLogoutSuccess = () => {
  if (window.$customToast) {
    return window.$customToast.info(
      '已退出登录',
      '感谢您的使用，期待下次见面',
      {
        duration: 3000,
        showProgress: true,
      }
    );
  }

  showToast('已成功退出登录', 'success');
};

// 专门用于操作成功的提示
export const showOperationSuccess = (operation, details) => {
  if (window.$customToast) {
    return window.$customToast.success(
      `${operation}成功`,
      details,
      {
        duration: 3000,
        showProgress: true,
      }
    );
  }

  showToast(`${operation}成功`, 'success');
};

// 专门用于错误提示的美化
export const showErrorToast = (title, description) => {
  if (window.$customToast) {
    return window.$customToast.error(
      title,
      description,
      {
        duration: 5000,
        showProgress: true,
      }
    );
  }

  showToast(title, 'error');
};

export const showError = message => {
  showToast(message, 'error');
};

export const showWarning = (message, description) => {
  if (window.$customToast && description) {
    return window.$customToast.warning(message, description, {
      duration: 4000,
      showProgress: true,
    });
  }

  showToast(message, 'warning');
};

export const showInfo = (message, description) => {
  if (window.$customToast && description) {
    return window.$customToast.info(message, description, {
      duration: 3000,
      showProgress: true,
    });
  }

  showToast(message, 'info');
};
