{"_attachments": {}, "_id": "is-plain-object", "_rev": "1533-61f1469cfbcaa28a7594c6fc", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "description": "Returns true if an object was created by the `Object` constructor, or Object.create(null).", "dist-tags": {"latest": "5.0.0"}, "license": "MIT", "maintainers": [{"name": "trysound", "email": "<EMAIL>"}, {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "name": "is-plain-object", "readme": "# is-plain-object [![NPM version](https://img.shields.io/npm/v/is-plain-object.svg?style=flat)](https://www.npmjs.com/package/is-plain-object) [![NPM monthly downloads](https://img.shields.io/npm/dm/is-plain-object.svg?style=flat)](https://npmjs.org/package/is-plain-object) [![NPM total downloads](https://img.shields.io/npm/dt/is-plain-object.svg?style=flat)](https://npmjs.org/package/is-plain-object) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/is-plain-object.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/is-plain-object)\n\n> Returns true if an object was created by the `Object` constructor, or Object.create(null).\n\nPlease consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.\n\n## Install\n\nInstall with [npm](https://www.npmjs.com/):\n\n```sh\n$ npm install --save is-plain-object\n```\n\nUse [isobject](https://github.com/jonschlinkert/isobject) if you only want to check if the value is an object and not an array or null.\n\n## Usage\n\nwith es modules\n```js\nimport { isPlainObject } from 'is-plain-object';\n```\n\nor with commonjs\n```js\nconst { isPlainObject } = require('is-plain-object');\n```\n\n**true** when created by the `Object` constructor, or Object.create(null).\n\n```js\nisPlainObject(Object.create({}));\n//=> true\nisPlainObject(Object.create(Object.prototype));\n//=> true\nisPlainObject({foo: 'bar'});\n//=> true\nisPlainObject({});\n//=> true\nisPlainObject(null);\n//=> true\n```\n\n**false** when not created by the `Object` constructor.\n\n```js\nisPlainObject(1);\n//=> false\nisPlainObject(['foo', 'bar']);\n//=> false\nisPlainObject([]);\n//=> false\nisPlainObject(new Foo);\n//=> false\nisPlainObject(Object.create(null));\n//=> false\n```\n\n## About\n\n<details>\n<summary><strong>Contributing</strong></summary>\n\nPull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).\n\n</details>\n\n<details>\n<summary><strong>Running Tests</strong></summary>\n\nRunning and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:\n\n```sh\n$ npm install && npm test\n```\n\n</details>\n\n<details>\n<summary><strong>Building docs</strong></summary>\n\n_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_\n\nTo generate the readme, run the following command:\n\n```sh\n$ npm install -g verbose/verb#dev verb-generate-readme && verb\n```\n\n</details>\n\n### Related projects\n\nYou might also be interested in these projects:\n\n* [is-number](https://www.npmjs.com/package/is-number): Returns true if a number or string value is a finite number. Useful for regex… [more](https://github.com/jonschlinkert/is-number) | [homepage](https://github.com/jonschlinkert/is-number \"Returns true if a number or string value is a finite number. Useful for regex matches, parsing, user input, etc.\")\n* [isobject](https://www.npmjs.com/package/isobject): Returns true if the value is an object and not an array or null. | [homepage](https://github.com/jonschlinkert/isobject \"Returns true if the value is an object and not an array or null.\")\n* [kind-of](https://www.npmjs.com/package/kind-of): Get the native type of a value. | [homepage](https://github.com/jonschlinkert/kind-of \"Get the native type of a value.\")\n\n### Contributors\n\n| **Commits** | **Contributor** |  \n| --- | --- |  \n| 19 | [jonschlinkert](https://github.com/jonschlinkert) |  \n| 6  | [TrySound](https://github.com/TrySound) |  \n| 6  | [stevenvachon](https://github.com/stevenvachon) |  \n| 3  | [onokumus](https://github.com/onokumus) |  \n| 1  | [wtgtybhertgeghgtwtg](https://github.com/wtgtybhertgeghgtwtg) |  \n\n### Author\n\n**Jon Schlinkert**\n\n* [GitHub Profile](https://github.com/jonschlinkert)\n* [Twitter Profile](https://twitter.com/jonschlinkert)\n* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)\n\n### License\n\nCopyright © 2019, [Jon Schlinkert](https://github.com/jonschlinkert).\nReleased under the [MIT License](LICENSE).\n\n***\n\n_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.8.0, on April 28, 2019._\n", "time": {"created": "2022-01-26T13:03:24.285Z", "modified": "2025-07-13T15:50:46.336Z", "5.0.0": "2020-09-09T16:30:03.627Z", "4.1.1": "2020-07-24T06:55:15.761Z", "4.1.0": "2020-07-21T10:59:40.572Z", "4.0.0": "2020-07-21T00:09:53.219Z", "3.0.1": "2020-06-27T08:06:19.703Z", "3.0.0": "2019-04-30T20:53:56.159Z", "2.0.4": "2017-07-11T22:41:10.371Z", "2.0.3": "2017-05-30T14:06:48.757Z", "2.0.2": "2017-05-27T19:53:30.152Z", "2.0.1": "2015-05-28T08:02:18.730Z", "2.0.0": "2015-04-29T12:57:28.225Z", "1.0.0": "2015-02-25T07:49:39.188Z", "0.1.0": "2014-09-21T22:41:50.073Z"}, "versions": {"5.0.0": {"name": "is-plain-object", "description": "Returns true if an object was created by the `Object` constructor, or Object.create(null).", "version": "5.0.0", "homepage": "https://github.com/jonschlinkert/is-plain-object", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON>", "url": "http://onokumus.com"}, {"name": "<PERSON>", "url": "https://svachon.com"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/TrySound"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-plain-object.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "license": "MIT", "main": "dist/is-plain-object.js", "module": "dist/is-plain-object.mjs", "types": "is-plain-object.d.ts", "exports": {".": {"import": "./dist/is-plain-object.mjs", "require": "./dist/is-plain-object.js"}, "./package.json": "./package.json"}, "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test_browser": "mocha-headless-chrome --args=disable-web-security -f test/browser.html", "test_node": "mocha -r esm", "test": "npm run test_node && npm run build && npm run test_browser", "prepare": "rollup -c"}, "devDependencies": {"chai": "^4.2.0", "esm": "^3.2.22", "gulp-format-md": "^1.0.0", "mocha": "^6.1.4", "mocha-headless-chrome": "^3.1.0", "rollup": "^2.22.1"}, "keywords": ["check", "is", "is-object", "isobject", "javascript", "kind", "kind-of", "object", "plain", "type", "typeof", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-number", "isobject", "kind-of"]}, "lint": {"reflinks": true}}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "is-plain-object@5.0.0", "dist": {"shasum": "4427f50ab3429e9025ea7d52e9043a9ef4159344", "size": 3408, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-5.0.0.tgz", "integrity": "sha512-VRSzKkbMm5jMDoKLbltAkFQ5Qr7VDiTFGXxYFXXowVj387GeGNOCsOH6Msy00SGZ3Fp84b1Naa1psqgcCIEP5Q=="}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "trysound", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-plain-object_5.0.0_1599669003462_0.7889028160747054"}, "_hasShrinkwrap": false, "publish_time": 1599669003627, "_cnpm_publish_time": 1599669003627, "_cnpmcore_publish_time": "2021-12-13T13:40:11.605Z"}, "4.1.1": {"name": "is-plain-object", "description": "Returns true if an object was created by the `Object` constructor, or Object.create(null).", "version": "4.1.1", "homepage": "https://github.com/jonschlinkert/is-plain-object", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON>", "url": "http://onokumus.com"}, {"name": "<PERSON>", "url": "https://svachon.com"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/TrySound"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-plain-object.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "license": "MIT", "main": "index.cjs.js", "module": "index.es.js", "types": "index.d.ts", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test_browser": "mocha-headless-chrome --args=disable-web-security -f test/browser.html", "test_node": "mocha -r esm", "test": "npm run test_node && npm run build && npm run test_browser", "prepare": "rollup -c"}, "devDependencies": {"chai": "^4.2.0", "esm": "^3.2.22", "gulp-format-md": "^1.0.0", "mocha": "^6.1.4", "mocha-headless-chrome": "^3.1.0", "rollup": "^2.22.1"}, "keywords": ["check", "is", "is-object", "isobject", "javascript", "kind", "kind-of", "object", "plain", "type", "typeof", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-number", "isobject", "kind-of"]}, "lint": {"reflinks": true}}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "is-plain-object@4.1.1", "dist": {"shasum": "1a14d6452cbd50790edc7fdaa0aed5a40a35ebb5", "size": 3291, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-4.1.1.tgz", "integrity": "sha512-5Aw8LLVsDlZsETVMhoMXzqsXwQqr/0vlnBYzIXJbYo2F4yYlhLHs+Ez7Bod7IIQKWkJbJfxrWD7pA1Dw1TKrwA=="}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "trysound", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-plain-object_4.1.1_1595573715637_0.6347161567543831"}, "_hasShrinkwrap": false, "publish_time": 1595573715761, "_cnpm_publish_time": 1595573715761, "_cnpmcore_publish_time": "2021-12-13T13:40:11.914Z"}, "4.1.0": {"name": "is-plain-object", "description": "Returns true if an object was created by the `Object` constructor, or Object.create(null).", "version": "4.1.0", "homepage": "https://github.com/jonschlinkert/is-plain-object", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON>", "url": "http://onokumus.com"}, {"name": "<PERSON>", "url": "https://svachon.com"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/TrySound"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-plain-object.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "license": "MIT", "main": "index.cjs.js", "module": "index.es.js", "types": "index.d.ts", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test_browser": "mocha-headless-chrome --args=disable-web-security -f test/browser.html", "test_node": "mocha -r esm", "test": "npm run test_node && npm run build && npm run test_browser", "prepare": "rollup -c"}, "devDependencies": {"chai": "^4.2.0", "esm": "^3.2.22", "gulp-format-md": "^1.0.0", "mocha": "^6.1.4", "mocha-headless-chrome": "^3.1.0", "rollup": "^2.22.1"}, "keywords": ["check", "is", "is-object", "isobject", "javascript", "kind", "kind-of", "object", "plain", "type", "typeof", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-number", "isobject", "kind-of"]}, "lint": {"reflinks": true}}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "is-plain-object@4.1.0", "dist": {"shasum": "00196ad308ebb7de9d1fb57ae92ef1c38d5a740e", "size": 3296, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-4.1.0.tgz", "integrity": "sha512-1N1OpoS8S4Ua+FsH6Mhvgaj0di3uRXgulcv2dnFu2J/WcEsDNbBoiUX6mYmhQ2cAzZ+B/lTJtX1qUSL5RwsGug=="}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "trysound", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-plain-object_4.1.0_1595329180429_0.6450668912349269"}, "_hasShrinkwrap": false, "publish_time": 1595329180572, "_cnpm_publish_time": 1595329180572, "_cnpmcore_publish_time": "2021-12-13T13:40:12.238Z"}, "4.0.0": {"name": "is-plain-object", "description": "Returns true if an object was created by the `Object` constructor, or Object.create(null).", "version": "4.0.0", "homepage": "https://github.com/jonschlinkert/is-plain-object", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON>", "url": "http://onokumus.com"}, {"name": "<PERSON>", "url": "https://svachon.com"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/TrySound"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-plain-object.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "license": "MIT", "main": "index.cjs.js", "module": "index.es.js", "types": "index.d.ts", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test_browser": "mocha-headless-chrome --args=disable-web-security -f test/browser.html", "test_node": "mocha -r esm", "test": "npm run test_node && npm run build && npm run test_browser", "prepare": "rollup -c"}, "devDependencies": {"@rollup/plugin-node-resolve": "^8.1.0", "chai": "^4.2.0", "esm": "^3.2.22", "gulp-format-md": "^1.0.0", "isobject": "^4.0.0", "mocha": "^6.1.4", "mocha-headless-chrome": "^3.1.0", "rollup": "^2.18.1"}, "keywords": ["check", "is", "is-object", "isobject", "javascript", "kind", "kind-of", "object", "plain", "type", "typeof", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-number", "isobject", "kind-of"]}, "lint": {"reflinks": true}}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "is-plain-object@4.0.0", "dist": {"shasum": "2cd131aa2dc1340ceaee89248f61823706457feb", "size": 3409, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-4.0.0.tgz", "integrity": "sha512-WipTQmPd1scuJUgwV/EZ1QbbOpEyHTQEXz4cJvrn+uwwgUtPcd3FD+yvAcKLYA8so2UsjNAq1JBu9rGgmGguVg=="}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "trysound", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-plain-object_4.0.0_1595290193115_0.9493888596664919"}, "_hasShrinkwrap": false, "publish_time": 1595290193219, "_cnpm_publish_time": 1595290193219, "_cnpmcore_publish_time": "2021-12-13T13:40:12.579Z"}, "3.0.1": {"name": "is-plain-object", "description": "Returns true if an object was created by the `Object` constructor.", "version": "3.0.1", "homepage": "https://github.com/jonschlinkert/is-plain-object", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON>", "url": "http://onokumus.com"}, {"name": "<PERSON>", "url": "https://svachon.com"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/TrySound"}], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-plain-object.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "license": "MIT", "main": "index.cjs.js", "module": "index.es.js", "types": "index.d.ts", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test_browser": "mocha-headless-chrome --args=disable-web-security -f test/browser.html", "test_node": "mocha -r esm", "test": "npm run test_node && npm run build && npm run test_browser", "prepare": "rollup -c"}, "devDependencies": {"@rollup/plugin-node-resolve": "^8.1.0", "chai": "^4.2.0", "esm": "^3.2.22", "gulp-format-md": "^1.0.0", "isobject": "^4.0.0", "mocha": "^6.1.4", "mocha-headless-chrome": "^3.1.0", "rollup": "^1.10.1"}, "keywords": ["check", "is", "is-object", "isobject", "javascript", "kind", "kind-of", "object", "plain", "type", "typeof", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-number", "isobject", "kind-of"]}, "lint": {"reflinks": true}}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2014-2017, <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\nTHE SOFTWARE.\n", "_id": "is-plain-object@3.0.1", "dist": {"shasum": "662d92d24c0aa4302407b0d45d21f2251c85f85b", "size": 3367, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-3.0.1.tgz", "integrity": "sha512-Xnpx182SBMrr/aBik8y+GuR4U1L9FqMSojwDQwPMmxyC6bvEqly9UBCxhauBF5vNh2gwWJNX6oDV7O+OM4z34g=="}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "_npmUser": {"name": "trysound", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-plain-object_3.0.1_1593245179588_0.38683699163270036"}, "_hasShrinkwrap": false, "publish_time": 1593245179703, "_cnpm_publish_time": 1593245179703, "_cnpmcore_publish_time": "2021-12-13T13:40:12.894Z"}, "3.0.0": {"name": "is-plain-object", "description": "Returns true if an object was created by the `Object` constructor.", "version": "3.0.0", "homepage": "https://github.com/jonschlinkert/is-plain-object", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON>", "url": "http://onokumus.com"}, {"name": "<PERSON>", "url": "https://svachon.com"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-plain-object.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "license": "MIT", "main": "index.cjs.js", "module": "index.js", "types": "index.d.ts", "engines": {"node": ">=0.10.0"}, "scripts": {"build": "rollup -c", "test_browser": "mocha-headless-chrome --args=disable-web-security -f test/browser.html", "test_node": "mocha -r esm", "test": "npm run test_node && npm run build && npm run test_browser", "prepare": "rollup -c"}, "dependencies": {"isobject": "^4.0.0"}, "devDependencies": {"chai": "^4.2.0", "esm": "^3.2.22", "gulp-format-md": "^1.0.0", "mocha": "^6.1.4", "mocha-headless-chrome": "^2.0.2", "rollup": "^1.10.1", "rollup-plugin-node-resolve": "^4.2.3"}, "keywords": ["check", "is", "is-object", "isobject", "javascript", "kind", "kind-of", "object", "plain", "type", "typeof", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-number", "isobject", "kind-of"]}, "lint": {"reflinks": true}}, "gitHead": "340bc4ee0eba9322b85bbf732603c3f7fe67851c", "_id": "is-plain-object@3.0.0", "_npmVersion": "6.4.1", "_nodeVersion": "8.15.0", "_npmUser": {"name": "trysound", "email": "<EMAIL>"}, "dist": {"shasum": "47bfc5da1b5d50d64110806c199359482e75a928", "size": 3328, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-3.0.0.tgz", "integrity": "sha512-tZIpofR+P05k8Aocp7UI/2UTa9lTJSebCXpFFoR9aibpokDj/uXBsJ8luUu0tTVYKkMU6URDUuOfJZ7koewXvg=="}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-plain-object_3.0.0_1556657636003_0.8407783091812069"}, "_hasShrinkwrap": false, "publish_time": 1556657636159, "_cnpm_publish_time": 1556657636159, "_cnpmcore_publish_time": "2021-12-13T13:40:13.256Z"}, "2.0.4": {"name": "is-plain-object", "description": "Returns true if an object was created by the `Object` constructor.", "version": "2.0.4", "homepage": "https://github.com/jonschlinkert/is-plain-object", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON>", "url": "http://onokumus.com"}, {"name": "<PERSON>", "url": "https://svachon.com"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-plain-object.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "license": "MIT", "files": ["index.d.ts", "index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"browserify": "browserify index.js --standalone isPlainObject | uglifyjs --compress --mangle -o browser/is-plain-object.js", "test_browser": "mocha-phantomjs test/browser.html", "test_node": "mocha", "test": "npm run test_node && npm run browserify && npm run test_browser"}, "dependencies": {"isobject": "^3.0.1"}, "devDependencies": {"browserify": "^14.4.0", "chai": "^4.0.2", "gulp-format-md": "^1.0.0", "mocha": "^3.4.2", "mocha-phantomjs": "^4.1.0", "phantomjs": "^2.1.7", "uglify-js": "^3.0.24"}, "keywords": ["check", "is", "is-object", "isobject", "javascript", "kind", "kind-of", "object", "plain", "type", "typeof", "value"], "types": "index.d.ts", "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["is-number", "isobject", "kind-of"]}, "lint": {"reflinks": true}}, "gitHead": "81345df0d1700a5c285f379cbdca0e273388910d", "_id": "is-plain-object@2.0.4", "_npmVersion": "5.2.0", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "dist": {"shasum": "2c163b3fafb1b606d9d17928f05c2a1c38e07677", "size": 3029, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-plain-object-2.0.4.tgz_1499812869259_0.27965074591338634"}, "directories": {}, "publish_time": 1499812870371, "_hasShrinkwrap": false, "_cnpm_publish_time": 1499812870371, "_cnpmcore_publish_time": "2021-12-13T13:40:13.540Z"}, "2.0.3": {"name": "is-plain-object", "description": "Returns true if an object was created by the `Object` constructor.", "version": "2.0.3", "homepage": "https://github.com/jonschlinkert/is-plain-object", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "https://svachon.com"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-plain-object.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"browserify": "browserify index.js --standalone isPlainObject | uglifyjs --compress --mangle -o browser/is-plain-object.js", "test_browser": "mocha-phantomjs test/browser.html", "test_node": "mocha", "test": "npm run test_node && npm run browserify && npm run test_browser"}, "dependencies": {"isobject": "^3.0.0"}, "devDependencies": {"browserify": "^14.3.0", "chai": "^4.0.0", "gulp-format-md": "^0.1.12", "mocha": "^3.4.2", "mocha-phantomjs": "^4.1.0", "phantomjs": "^2.1.7", "uglify-js": "^3.0.12"}, "keywords": ["check", "is", "is-object", "isobject", "javascript", "kind", "kind-of", "object", "plain", "type", "typeof", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["isobject", "is-number", "kind-of"]}, "lint": {"reflinks": true}}, "gitHead": "6616250bedd50e6f2378b44637875f22178f7506", "_id": "is-plain-object@2.0.3", "_shasum": "c15bf3e4b66b62d72efaf2925848663ecbc619b6", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "dist": {"shasum": "c15bf3e4b66b62d72efaf2925848663ecbc619b6", "size": 2875, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-2.0.3.tgz", "integrity": "sha512-4QumWYGOyh/PBXAjqTavDrW61AXQMo9MddhHneiqHZkeql0uKV6ytfEPQuZFNf/hoaRi8ooNYFvQBC5tI3i8xA=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-plain-object-2.0.3.tgz_1496153207864_0.18937807087786496"}, "directories": {}, "publish_time": 1496153208757, "_hasShrinkwrap": false, "_cnpm_publish_time": 1496153208757, "_cnpmcore_publish_time": "2021-12-13T13:40:13.947Z"}, "2.0.2": {"name": "is-plain-object", "description": "Returns true if an object was created by the `Object` constructor.", "version": "2.0.2", "homepage": "https://github.com/jonschlinkert/is-plain-object", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "https://svachon.com"}], "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/is-plain-object.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"browserify": "browserify index.js --standalone isPlainObject | uglifyjs --compress --mangle -o browser/is-plain-object.js", "test_browser": "mocha-phantomjs test/browser.html", "test_node": "mocha", "test": "npm run test_node && npm run browserify && npm run test_browser"}, "dependencies": {"isobject": "^3.0.0"}, "devDependencies": {"browserify": "^14.3.0", "gulp-format-md": "^0.1.12", "mocha": "^3.4.2", "mocha-phantomjs": "^4.1.0", "phantomjs": "^2.1.7", "uglify-js": "^3.0.12"}, "keywords": ["check", "is", "is-object", "isobject", "javascript", "kind", "kind-of", "object", "plain", "type", "typeof", "value"], "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["isobject", "is-number", "kind-of"]}, "lint": {"reflinks": true}}, "gitHead": "e6ffcb78603dc3a3826e75852c449639bca627ef", "_id": "is-plain-object@2.0.2", "_shasum": "1d9ab795669937de31998071ca1f701770b375a4", "_from": ".", "_npmVersion": "4.6.1", "_nodeVersion": "7.7.3", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "dist": {"shasum": "1d9ab795669937de31998071ca1f701770b375a4", "size": 2864, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-2.0.2.tgz", "integrity": "sha512-0LshiloqaVPchDPbWL/5j2pNXuveiywMgjSXzB8IPYx22XnWBuOoQr9yoAZvw1XUEwRDzejOnX0nELlObuKaxA=="}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/is-plain-object-2.0.2.tgz_1495914809154_0.5315580435562879"}, "directories": {}, "publish_time": 1495914810152, "_hasShrinkwrap": false, "_cnpm_publish_time": 1495914810152, "_cnpmcore_publish_time": "2021-12-13T13:40:14.316Z"}, "2.0.1": {"name": "is-plain-object", "description": "Returns true if an object was created by the `Object` constructor.", "version": "2.0.1", "homepage": "https://github.com/jonschlinkert/is-plain-object", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-plain-object"}, "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"browserify": "browserify index.js --standalone isPlainObject | uglifyjs --compress --mangle -o browser/is-plain-object.js", "test_browser": "mocha-phantomjs test/browser.html", "test_node": "mocha", "test": "npm run test_node && npm run browserify && npm run test_browser"}, "dependencies": {"isobject": "^1.0.0"}, "devDependencies": {"browserify": "*", "chai": "*", "mocha": "*", "mocha-phantomjs": "*", "phantomjs": "*", "uglify-js": "*"}, "keywords": ["object", "is", "is-object", "isobject", "plain", "value", "type", "kind", "kind-of", "typeof", "javascript", "check", "type"], "gitHead": "b96e86b84e78e21108b1ea824a3e91af1e00164b", "_id": "is-plain-object@2.0.1", "_shasum": "4d7ca539bc9db9b737b8acb612f2318ef92f294f", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "dist": {"shasum": "4d7ca539bc9db9b737b8acb612f2318ef92f294f", "size": 2395, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-2.0.1.tgz", "integrity": "sha512-F/FLn/qy/gwj4RWlHhdO+lc1ACqkIPqwxP9TlnsNJerkqvFNLlTQoos+1MxIklqdwGutrmzV6SUPiDrkiBpB6g=="}, "directories": {}, "publish_time": 1432800138730, "_hasShrinkwrap": false, "_cnpm_publish_time": 1432800138730, "_cnpmcore_publish_time": "2021-12-13T13:40:14.743Z"}, "2.0.0": {"name": "is-plain-object", "description": "Returns true if an object was created by the `Object` constructor.", "version": "2.0.0", "homepage": "https://github.com/jonschlinkert/is-plain-object", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-plain-object.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/is-plain-object/blob/master/LICENSE"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"browserify": "browserify index.js --standalone isPlainObject | uglifyjs --compress --mangle -o browser/is-plain-object.js", "test_browser": "mocha-phantomjs test/browser.html", "test_node": "mocha test", "test": "npm run test_node && npm run browserify && npm run test_browser"}, "dependencies": {"isobject": "^0.2.0"}, "devDependencies": {"browserify": "*", "chai": "*", "mocha": "*", "mocha-phantomjs": "*", "phantomjs": "*", "uglify-js": "*"}, "keywords": ["object", "is", "is-object", "isobject", "plain", "value", "typeof", "javascript", "check", "type"], "gitHead": "b96e86b84e78e21108b1ea824a3e91af1e00164b", "_id": "is-plain-object@2.0.0", "_shasum": "8612587fa90279dc1b6e1cec2056f6c1df7abb2a", "_from": "git://github.com/jonschlinkert/is-plain-object.git", "_resolved": "git://github.com/jonschlinkert/is-plain-object.git#b96e86b84e78e21108b1ea824a3e91af1e00164b", "_fromGithub": true, "_npmVersion": "2.7.4", "_nodeVersion": "0.12.2", "_npmUser": {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "dist": {"shasum": "8612587fa90279dc1b6e1cec2056f6c1df7abb2a", "size": 2103, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-2.0.0.tgz", "integrity": "sha512-HwcxEMZP726Fk8+02WDquQHSd95nifNqIvT23+FfQh4LxW+spxD/E+ZLe5GvyCsDEjc/sH/8kWNeeHfajlR+7Q=="}, "directories": {}, "publish_time": 1430312248225, "_hasShrinkwrap": false, "_cnpm_publish_time": 1430312248225, "_cnpmcore_publish_time": "2021-12-13T13:40:15.156Z"}, "1.0.0": {"name": "is-plain-object", "description": "Returns true if an object was created by the `Object` constructor.", "version": "1.0.0", "homepage": "https://github.com/jonschlinkert/is-plain-object", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-plain-object.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/is-plain-object/blob/master/LICENSE"}, "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"isobject": "^0.2.0"}, "devDependencies": {"mocha": "*"}, "keywords": ["object", "is", "is-object", "isobject", "plain", "value", "typeof", "javascript", "check", "type"], "gitHead": "48f326d18bcf8776c60cdf7c7859d2dc514e6257", "_id": "is-plain-object@1.0.0", "_shasum": "ff5f752db71c3328afd5e685eb6adddd3eaffab7", "_from": ".", "_npmVersion": "2.5.1", "_nodeVersion": "0.12.0", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "dist": {"shasum": "ff5f752db71c3328afd5e685eb6adddd3eaffab7", "size": 1793, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-1.0.0.tgz", "integrity": "sha512-+tXodG6I903VrzqxqKAQjA7ti9niaR18pza7uWPLmUvQfsUk+WzNwnu4JnW7UNT3idAcNTGMjZtvdrYTJnWkUA=="}, "directories": {}, "publish_time": 1424850579188, "_hasShrinkwrap": false, "_cnpm_publish_time": 1424850579188, "_cnpmcore_publish_time": "2021-12-13T13:40:15.568Z"}, "0.1.0": {"name": "is-plain-object", "description": "Return `true` if the given `value` is an object created by the `Object` constructor.", "version": "0.1.0", "homepage": "https://github.com/jonschlinkert/is-plain-object", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "repository": {"type": "git", "url": "git://github.com/jonschlinkert/is-plain-object.git"}, "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "licenses": [{"type": "MIT", "url": "https://github.com/jonschlinkert/is-plain-object/blob/master/LICENSE-MIT"}], "keywords": ["object", "is", "is-object", "isobject", "plain", "value", "typeof", "javascript", "check", "type"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha -R spec"}, "devDependencies": {"verb": "~0.2.6", "chai": "~1.9.1", "mocha": "*"}, "_id": "is-plain-object@0.1.0", "_shasum": "3ca7db022de72fd12007f1957beb59ea596b979c", "_from": ".", "_npmVersion": "1.4.9", "_npmUser": {"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "maintainers": [{"name": "j<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "ste<PERSON><PERSON><PERSON>n", "email": "<EMAIL>"}], "dist": {"shasum": "3ca7db022de72fd12007f1957beb59ea596b979c", "size": 2641, "noattachment": false, "tarball": "https://registry.npmmirror.com/is-plain-object/-/is-plain-object-0.1.0.tgz", "integrity": "sha512-pb50PSj2wtnl3sxe0uy8851298LRiZygwy48zRPwSIINOvDIZvO35ndeNTxFdNz5ilaWL37utyr3i+p9EeO4Sg=="}, "directories": {}, "publish_time": 1411339310073, "_hasShrinkwrap": false, "_cnpm_publish_time": 1411339310073, "_cnpmcore_publish_time": "2021-12-13T13:40:15.990Z"}}, "bugs": {"url": "https://github.com/jonschlinkert/is-plain-object/issues"}, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON><PERSON>", "url": "http://onokumus.com"}, {"name": "<PERSON>", "url": "https://svachon.com"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/TrySound"}], "homepage": "https://github.com/jonschlinkert/is-plain-object", "keywords": ["check", "is", "is-object", "isobject", "javascript", "kind", "kind-of", "object", "plain", "type", "typeof", "value"], "repository": {"type": "git", "url": "https://github.com/jonschlinkert/is-plain-object.git"}, "_source_registry_name": "default"}