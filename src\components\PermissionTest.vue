<template>
  <div class="permission-test">
    <h3>权限控制测试</h3>
    
    <div class="test-section">
      <h4>1. 我的最近任务列表（需要登录）</h4>
      <PermissionGuard 
        feature="my-recent-tasks" 
        :requiresLogin="true"
      >
        <div class="test-content">
          <p>✅ 这是我的最近任务列表内容</p>
          <ul>
            <li>任务1：完成用户界面设计</li>
            <li>任务2：实现登录功能</li>
            <li>任务3：优化数据库查询</li>
          </ul>
        </div>
      </PermissionGuard>
    </div>

    <div class="test-section">
      <h4>2. 我的项目列表（需要登录）</h4>
      <PermissionGuard 
        feature="my-projects" 
        :requiresLogin="true"
      >
        <div class="test-content">
          <p>✅ 这是我的项目列表内容</p>
          <ul>
            <li>项目1：项目管理系统</li>
            <li>项目2：电商平台</li>
            <li>项目3：移动应用开发</li>
          </ul>
        </div>
      </PermissionGuard>
    </div>

    <div class="test-section">
      <h4>3. 人员管理（需要管理员权限）</h4>
      <PermissionGuard 
        permission="personnel" 
        :role="['admin', 'manager', 'project_manager']"
      >
        <div class="test-content">
          <p>✅ 这是人员管理内容</p>
          <p>只有管理员、经理和项目经理可以看到这个内容</p>
        </div>
      </PermissionGuard>
    </div>

    <div class="test-section">
      <h4>4. 普通功能（所有人可见）</h4>
      <div class="test-content">
        <p>✅ 这是普通功能内容</p>
        <p>所有用户（包括未登录用户）都可以看到这个内容</p>
      </div>
    </div>

    <div class="test-section">
      <h4>5. 需要登录的功能</h4>
      <PermissionGuard :requiresLogin="true">
        <div class="test-content">
          <p>✅ 这是需要登录的功能内容</p>
          <p>只有登录用户可以看到这个内容</p>
        </div>
      </PermissionGuard>
    </div>

    <div class="test-info">
      <h4>当前状态</h4>
      <p><strong>登录状态:</strong> {{ isAuthenticated ? '已登录' : '未登录' }}</p>
      <p v-if="isAuthenticated"><strong>用户角色:</strong> {{ userRole }}</p>
      <p><strong>权限列表:</strong> {{ permissions.join(', ') || '无权限' }}</p>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useStore } from 'vuex';
import PermissionGuard from './PermissionGuard.vue';

const store = useStore();

const isAuthenticated = computed(() => store.getters.isAuthenticated);
const userRole = computed(() => store.getters.userRole);
const permissions = computed(() => store.getters.permissions);
</script>

<style scoped>
.permission-test {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.permission-test h3 {
  color: #00ffff;
  margin-bottom: 2rem;
  text-align: center;
}

.test-section {
  margin-bottom: 2rem;
  border: 1px solid #374151;
  border-radius: 8px;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
}

.test-section h4 {
  color: #00ffff;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.test-content {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid #10b981;
  border-radius: 6px;
  padding: 1rem;
  color: #10b981;
}

.test-content p {
  margin-bottom: 0.5rem;
}

.test-content ul {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.test-content li {
  margin-bottom: 0.25rem;
}

.test-info {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid #3b82f6;
  border-radius: 8px;
  padding: 1rem;
  margin-top: 2rem;
}

.test-info h4 {
  color: #3b82f6;
  margin-bottom: 1rem;
}

.test-info p {
  color: #e5e7eb;
  margin-bottom: 0.5rem;
}

.test-info strong {
  color: #00ffff;
}
</style>
