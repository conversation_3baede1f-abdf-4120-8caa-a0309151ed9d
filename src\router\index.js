import { createRouter, createWebHistory } from 'vue-router';
import store from '../store';
import MainLayout from '../layout/MainLayout.vue';
import ProjectDashboard from '../views/Dashboard.vue';
import ProjectManagement from '../views/Projects.vue';
import TaskManagement from '../views/Tasks.vue';
import UserManagement from '../views/Users.vue';
import UserDashboard from '../views/UserDashboard.vue';

const routes = [
  {
    path: '/',
    redirect: '/dashboard',
  },
  {
    path: '/',
    component: MainLayout,
    children: [
      {
        path: '/dashboard',
        component: ProjectDashboard,
        meta: {
          requiresAuth: false,
          permission: 'dashboard'
        },
      },
      {
        path: '/projects',
        component: ProjectManagement,
        meta: {
          requiresAuth: true,
          permission: 'projects'
        },
      },
      {
        path: '/tasks',
        component: TaskManagement,
        meta: {
          requiresAuth: true,
          permission: 'tasks'
        },
      },
      {
        path: '/users',
        component: UserManagement,
        meta: {
          requiresAuth: true,
          permission: 'personnel',
          requiresRole: ['admin', 'manager', 'project_manager']
        },
      },
      {
        path: '/UserDashboard',
        component: UserDashboard,
        meta: {
          requiresAuth: true,
          permission: 'tasks'
        },
      },
    ],
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/LoginModal.vue'),
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 添加全局前置守卫
router.beforeEach((to, _from, next) => {
  const token = localStorage.getItem('token');
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || 'null');
  const userRole = userInfo?.role || userInfo?.userType || 'guest';

  // 仪表盘所有人都可以访问
  if (to.path === '/dashboard') {
    next();
    return;
  }

  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    if (!token) {
      // 未登录用户尝试访问需要登录的页面，重定向到仪表盘
      console.log('未登录用户尝试访问需要登录的页面，重定向到仪表盘');
      next('/dashboard');
      return;
    }

    // 检查人员管理权限
    if (to.meta.requiresRole) {
      const normalizedRole = userRole.toLowerCase();
      const allowedRoles = to.meta.requiresRole.map(role => role.toLowerCase());

      if (!allowedRoles.includes(normalizedRole)) {
        console.log('权限不足，无法访问人员管理页面，重定向到仪表盘');
        next('/dashboard');
        return;
      }
    }

    // 检查具体权限
    if (to.meta.permission && to.meta.permission !== 'dashboard') {
      const permissions = JSON.parse(localStorage.getItem('permissions') || '[]');
      if (!permissions.includes(to.meta.permission)) {
        console.log('缺少必要权限，无法访问此页面，重定向到仪表盘');
        next('/dashboard');
        return;
      }
    }
  }

  // 已登录用户访问登录页重定向到仪表盘
  if (to.path === '/login' && token) {
    next('/dashboard');
    return;
  }

  next();
});

export default router;
